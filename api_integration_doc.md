# 流程挖掘分析报告API对接文档

## 1. 系统概述

### 1.1 产品介绍
流程挖掘分析报告API是一个基于FastAPI框架开发的智能分析系统，能够接收流程挖掘相关的JSON数据，通过AI技术自动生成专业的流程分析报告。系统支持异步处理、实时状态查询、多格式输出等功能。

### 1.2 主要功能
- **智能报告生成**：基于AI技术，自动分析流程挖掘数据并生成专业报告
- **异步处理**：支持大数据量的异步处理，避免请求超时
- **多领域支持**：支持金融、医疗、物流等不同领域的定制化分析
- **实时状态监控**：提供任务状态实时查询功能
- **灵活配置**：支持AI模型参数的动态配置
- **Web管理界面**：提供可视化的管理和测试界面

### 1.3 技术架构
- **后端框架**：FastAPI
- **异步处理**：BackgroundTasks + ThreadPoolExecutor
- **文件处理**：支持JSON数据处理和DOCX报告生成
- **AI集成**：支持多种AI模型接口
- **存储管理**：文件系统存储，支持自动清理

## 2. 环境要求

### 2.1 系统要求
- **操作系统**：Linux/Windows/macOS
- **Python版本**：3.8+
- **内存**：建议4GB以上
- **存储空间**：建议10GB以上

### 2.2 依赖包要求
```bash
pip install fastapi uvicorn python-docx lxml
```

### 2.3 配置要求
需要配置AI服务的相关参数（API_KEY、BASE_URL等）

## 3. 部署说明

### 3.1 启动服务
```bash
# 开发环境
python api_server.py

# 生产环境
uvicorn api.api_server:app --host 0.0.0.0 --port 8000
```

### 3.2 服务访问
- **API Base URL**：`http://your-server:8000`
- **Web管理界面**：`http://your-server:8000`
- **API文档**：`http://your-server:8000/docs`

## 4. API接口文档

### 4.1 数据处理接口

#### 4.1.1 提交JSON数据处理
**接口地址**：`POST /process_mining/process-json`

**请求参数**：
```json
{
  "json_data": {
    "process": "订单处理",
    "steps": ["接收订单", "确认支付", "发货", "完成"],
    "data": "您的流程挖掘数据"
  },
  "module_type": "auto",
  "output_format": "docx",
  "domain": "金融"
}
```

**参数说明**：
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| json_data | Object/Array | 是 | 流程挖掘数据，支持对象或数组格式 |
| module_type | String | 否 | 模块类型：auto(自动)/regular(常规)/simple(简单)，默认auto |
| output_format | String | 否 | 输出格式：docx，默认docx |
| domain | String | 否 | 分析领域：如金融、医疗、物流等，为空则为通用领域 |

**响应示例**：
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "pending",
  "message": "数据已接收，正在处理中"
}
```

#### 4.1.2 查询任务状态
**接口地址**：`GET /process_mining/task-status/{task_id}`

**响应示例**：
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "created_at": "2025-07-07T10:30:00",
  "completed_at": "2025-07-07T10:35:00",
  "output_file": "report_550e8400-e29b-41d4-a716-446655440000.docx",
  "error": null
}
```

**状态说明**：
- `pending`：待处理
- `processing`：处理中
- `completed`：已完成
- `failed`：处理失败

#### 4.1.3 下载报告
**接口地址**：`GET /process_mining/download-report/{task_id}`

**说明**：直接下载生成的报告文件，仅在任务状态为`completed`时可用。

### 4.2 任务管理接口

#### 4.2.1 获取任务列表
**接口地址**：`GET /process_mining/tasks`

**查询参数**：
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| status | String | 否 | 按状态过滤：pending/processing/completed/failed |
| limit | Integer | 否 | 每页数量，默认50 |
| offset | Integer | 否 | 偏移量，默认0 |

**响应示例**：
```json
{
  "total": 100,
  "limit": 50,
  "offset": 0,
  "tasks": [
    {
      "task_id": "550e8400-e29b-41d4-a716-446655440000",
      "status": "completed",
      "created_at": "2025-07-07T10:30:00",
      "completed_at": "2025-07-07T10:35:00",
      "output_file": "report_xxx.docx",
      "error": null
    }
  ]
}
```

#### 4.2.2 删除任务
**接口地址**：`DELETE /process_mining/task/{task_id}`

**响应示例**：
```json
{
  "message": "任务已删除"
}
```

### 4.3 配置管理接口

#### 4.3.1 获取AI配置
**接口地址**：`GET /process_mining/ai-config`

**响应示例**：
```json
{
  "api_key": "your-api-key",
  "base_url": "https://api.openai.com/v1",
  "model_name": "gpt-4",
  "max_tokens": 4000,
  "temperature": 0.7,
  "domain": "通用"
}
```

#### 4.3.2 更新AI配置
**接口地址**：`POST /process_mining/ai-config`

**请求参数**：
```json
{
  "api_key": "new-api-key",
  "base_url": "https://new-api.com/v1",
  "model_name": "gpt-4",
  "max_tokens": 4000,
  "temperature": 0.7,
  "domain": "金融"
}
```

**说明**：所有参数均为可选，只更新提供的参数。

## 5. 数据格式规范

### 5.1 输入数据格式
支持多种JSON数据格式：

#### 5.1.1 基础格式
```json
{
  "process": "流程名称",
  "description": "流程描述",
  "steps": ["步骤1", "步骤2", "步骤3"],
  "data": "原始数据或分析结果"
}
```

#### 5.1.2 详细格式
```json
{
  "process_info": {
    "name": "订单处理流程",
    "description": "电商订单处理业务流程",
    "domain": "电商"
  },
  "activities": [
    {
      "activity_id": "A1",
      "activity_name": "接收订单",
      "frequency": 1000,
      "duration": 5
    }
  ],
  "traces": [
    {
      "case_id": "C001",
      "activities": ["A1", "A2", "A3"],
      "timestamps": ["2025-01-01T10:00:00", "2025-01-01T10:05:00", "2025-01-01T10:15:00"]
    }
  ],
  "performance_metrics": {
    "total_cases": 500,
    "avg_duration": 30,
    "throughput": 100
  }
}
```

#### 5.1.3 数组格式
```json
[
  {
    "case_id": "001",
    "activity": "开始",
    "timestamp": "2025-01-01T10:00:00"
  },
  {
    "case_id": "001",
    "activity": "处理",
    "timestamp": "2025-01-01T10:05:00"
  }
]
```

### 5.2 输出格式
- **文件格式**：DOCX（Microsoft Word文档）
- **文件命名**：`流程挖掘分析报告_YYYYMMDD.docx`
- **内容结构**：包含目录、执行摘要、详细分析、图表、结论建议等章节

## 6. 错误处理

### 6.1 HTTP状态码
- `200`：请求成功
- `400`：请求参数错误
- `404`：资源不存在
- `422`：数据验证失败
- `500`：服务器内部错误

### 6.2 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

### 6.3 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| JSON格式错误 | 提交的数据不是有效的JSON | 检查JSON格式，确保语法正确 |
| 任务不存在 | 查询不存在的task_id | 确认task_id正确，或查询任务列表 |
| 报告未完成 | 尝试下载未完成的报告 | 等待任务完成后再下载 |
| 文件不存在 | 报告文件已被清理 | 重新生成报告 |
| 配置错误 | AI配置参数无效 | 检查API密钥和配置参数 |

## 7. 使用示例

### 7.1 Python示例
```python
import requests
import json
import time

# 1. 提交数据处理请求
api_base = "http://your-server:8000"
data = {
    "json_data": {
        "process": "订单处理",
        "steps": ["接收订单", "确认支付", "发货", "完成"],
        "data": "流程挖掘数据"
    },
    "module_type": "auto",
    "domain": "电商"
}

response = requests.post(f"{api_base}/process_mining/process-json", json=data)
result = response.json()
task_id = result["task_id"]
print(f"任务ID: {task_id}")

# 2. 轮询任务状态
while True:
    status_response = requests.get(f"{api_base}/process_mining/task-status/{task_id}")
    status = status_response.json()
    print(f"任务状态: {status['status']}")
    
    if status["status"] == "completed":
        print("任务完成，开始下载报告")
        break
    elif status["status"] == "failed":
        print(f"任务失败: {status.get('error', '未知错误')}")
        break
    
    time.sleep(5)  # 等待5秒后再次查询

# 3. 下载报告
if status["status"] == "completed":
    download_response = requests.get(f"{api_base}/process_mining/download-report/{task_id}")
    with open("report.docx", "wb") as f:
        f.write(download_response.content)
    print("报告下载完成")
```

### 7.2 JavaScript示例
```javascript
// 1. 提交数据处理请求
async function processData() {
    const apiBase = "http://your-server:8000";
    const data = {
        json_data: {
            process: "订单处理",
            steps: ["接收订单", "确认支付", "发货", "完成"],
            data: "流程挖掘数据"
        },
        module_type: "auto",
        domain: "电商"
    };
    
    const response = await fetch(`${apiBase}/process_mining/process-json`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    });
    
    const result = await response.json();
    const taskId = result.task_id;
    console.log(`任务ID: ${taskId}`);
    
    // 2. 轮询任务状态
    await pollTaskStatus(apiBase, taskId);
}

async function pollTaskStatus(apiBase, taskId) {
    while (true) {
        const response = await fetch(`${apiBase}/process_mining/task-status/${taskId}`);
        const status = await response.json();
        console.log(`任务状态: ${status.status}`);
        
        if (status.status === "completed") {
            console.log("任务完成，可以下载报告");
            window.open(`${apiBase}/process_mining/download-report/${taskId}`, '_blank');
            break;
        } else if (status.status === "failed") {
            console.log(`任务失败: ${status.error || '未知错误'}`);
            break;
        }
        
        await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒
    }
}
```

### 7.3 cURL示例
```bash
# 1. 提交数据处理请求
curl -X POST "http://your-server:8000/process_mining/process-json" \
     -H "Content-Type: application/json" \
     -d '{
       "json_data": {
         "process": "订单处理",
         "steps": ["接收订单", "确认支付", "发货", "完成"],
         "data": "流程挖掘数据"
       },
       "module_type": "auto",
       "domain": "电商"
     }'

# 2. 查询任务状态
curl -X GET "http://your-server:8000/process_mining/task-status/{task_id}"

# 3. 下载报告
curl -X GET "http://your-server:8000/process_mining/download-report/{task_id}" \
     -o "report.docx"
```

## 8. 性能和限制

### 8.1 性能指标
- **并发处理**：支持最多4个并发任务
- **处理时间**：根据数据量和复杂度，通常1-10分钟
- **文件大小**：建议单次提交数据不超过10MB
- **存储时间**：生成的文件保存24小时后自动清理

### 8.2 限制说明
- **数据格式**：仅支持JSON格式输入
- **输出格式**：目前仅支持DOCX格式
- **AI模型**：需要有效的AI API配置
- **网络要求**：需要稳定的网络连接访问AI服务

## 9. 安全考虑

### 9.1 数据安全
- 上传的数据文件会在24小时后自动删除
- 建议在生产环境中使用HTTPS协议
- API密钥等敏感信息应妥善保管

### 9.2 访问控制
- 建议在生产环境中添加身份验证机制
- 可通过反向代理添加访问限制
- 建议监控API使用情况

## 10. 故障排除

### 10.1 常见问题

**Q: 任务一直处于processing状态**
A: 检查AI服务配置是否正确，查看服务器日志获取详细错误信息。

**Q: 下载的报告目录显示为"{TOC}"**
A: 这是正常现象，在Word中打开文档后，右键点击目录区域选择"更新域"即可。

**Q: 提交大数据量时响应缓慢**
A: 系统采用异步处理，提交后可通过task_id查询状态，无需等待同步响应。

**Q: API返回422错误**
A: 检查请求数据格式，确保JSON格式正确且必填字段已提供。

### 10.2 日志查看
服务器端会记录详细的处理日志，可通过查看服务器日志获取更多错误信息。

## 11. 联系支持

如遇到技术问题或需要技术支持，请提供以下信息：
- 错误信息的完整描述
- 请求的数据格式和内容（脱敏后）
- 任务ID（如果有）
- 服务器日志信息

---

**版本信息**：v1.0.0  
**更新日期**：2025年7月7日  
**文档维护**：技术团队