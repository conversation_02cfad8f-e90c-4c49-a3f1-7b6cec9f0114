from setuptools import setup, find_packages

setup(
    name="flow-mining-analyzer",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        "python-docx>=0.8.11",
        "openai>=1.3.0",
        "python-dotenv>=1.0.0",
        "pandas>=1.5.0"
    ],
    entry_points={
        'console_scripts': [
            'flow-analyzer=flow_analyzer:main',
        ],
    },
    author="Your Name",
    author_email="<EMAIL>",
    description="流程挖掘分析报告生成器",
    keywords="process mining, flow analysis, report generation",
    python_requires=">=3.7",
)