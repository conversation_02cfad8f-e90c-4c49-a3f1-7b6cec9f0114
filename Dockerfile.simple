# 完全离线版本 - 使用本地wheel包，无网络依赖
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8

# 先复制wheel文件和requirements
COPY wheels_linux/ /tmp/wheels/
COPY requirements.txt .

# 完全离线安装，不访问网络
RUN pip install --no-index --find-links /tmp/wheels -r requirements.txt && \
    rm -rf /tmp/wheels

# 复制项目文件
COPY . .

# 创建必要的目录并设置权限
RUN mkdir -p /app/uploads /app/outputs /app/temp /app/logs /app/api/uploads /app/api/outputs /app/api/temp && \
    chmod 755 /app/uploads /app/outputs /app/temp /app/logs /app/api/uploads /app/api/outputs /app/api/temp

# 暴露端口
EXPOSE 8000

# 设置容器启动时的默认命令 - 启动Web API服务器
CMD ["python", "api/api_server.py"]