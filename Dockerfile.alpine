# 使用Alpine Linux作为基础镜像（更小更快）
FROM python:3.10-alpine

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 更换Alpine镜像源为阿里云镜像
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装系统依赖
RUN apk update && apk add --no-cache \
    gcc \
    g++ \
    musl-dev \
    libxml2-dev \
    libxslt-dev \
    libffi-dev \
    openssl-dev \
    curl \
    build-base \
    python3-dev \
    && rm -rf /var/cache/apk/*

# 复制pip配置文件
COPY pip.conf /etc/pip.conf

# 升级pip并配置国内镜像源
RUN pip install --upgrade pip setuptools wheel

# 复制requirements.txt文件并安装Python依赖（利用Docker层缓存）
COPY requirements.txt .

# 使用配置文件中的镜像源安装依赖，添加重试机制
RUN pip install --no-cache-dir -r requirements.txt || \
    pip install --no-cache-dir -r requirements.txt \
    -i https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com || \
    pip install --no-cache-dir -r requirements.txt \
    -i https://pypi.douban.com/simple/ \
    --trusted-host pypi.douban.com

# 复制项目文件
COPY . .

# 创建必要的目录并设置权限
RUN mkdir -p /app/uploads /app/outputs /app/temp /app/logs /app/api/uploads /app/api/outputs /app/api/temp && \
    chmod 755 /app/uploads /app/outputs /app/temp /app/logs /app/api/uploads /app/api/outputs /app/api/temp

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 设置容器启动时的默认命令 - 启动Web API服务器
CMD ["python", "api/api_server.py"]