# 流程挖掘分析报告生成器 (Java版本)

基于Spring Boot和AI技术的智能流程挖掘分析报告生成平台，能够自动分析JSON格式的流程数据并生成专业的Word文档报告。

## 功能特性

- ? **智能分析**: 基于AI技术自动分析流程挖掘数据
- ? **多模块支持**: 支持KPI指标、数据表格、流程图、变体分析等多种模块类型
- ? **自动报告生成**: 自动生成结构化的Word文档报告
- ? **领域定制**: 支持特定领域的专业术语和分析角度
- ? **Web界面**: 提供友好的Web上传和处理界面
- ? **异步处理**: 支持大文件的异步处理和进度跟踪

## 技术栈

- **后端框架**: Spring Boot 3.2.0
- **Java版本**: Java 17+
- **文档生成**: Apache POI
- **AI集成**: 通义千问 (Qwen) API
- **HTTP客户端**: Spring WebFlux
- **构建工具**: Maven
- **前端**: HTML5 + JavaScript (原生)

## 快速开始

### 环境要求

- Java 17 或更高版本
- Maven 3.6 或更高版本
- 通义千问API密钥

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd java-flow-mining-analyzer
   ```

2. **配置环境变量**
   ```bash
   export AI_API_KEY=your-qwen-api-key
   export AI_BASE_URL=https://dashscope.aliyuncs.com/api/v1
   ```

3. **编译项目**
   ```bash
   mvn clean compile
   ```

4. **运行应用**
   ```bash
   mvn spring-boot:run
   ```

5. **访问应用**
   打开浏览器访问: http://localhost:8080

### 配置说明

主要配置项在 `application.yml` 中：

```yaml
# AI配置
ai:
  api-key: ${AI_API_KEY:your-api-key-here}
  base-url: ${AI_BASE_URL:https://dashscope.aliyuncs.com/api/v1}
  model: ${AI_MODEL:qwen-plus}
  max-tokens: ${AI_MAX_TOKENS:2000}
  temperature: ${AI_TEMPERATURE:0.7}

# 应用配置
app:
  output-dir: ${APP_OUTPUT_DIR:./output}
  upload-dir: ${APP_UPLOAD_DIR:./uploads}
  temp-dir: ${APP_TEMP_DIR:./temp}
```

## 使用方法

### Web界面使用

1. 访问 http://localhost:8080
2. 上传JSON格式的流程挖掘数据文件
3. 可选择指定分析领域（如：制造业、金融、医疗等）
4. 点击"开始分析"按钮
5. 等待处理完成后下载生成的Word报告

### API接口使用

#### 上传文件处理
```bash
curl -X POST http://localhost:8080/api/process-mining/upload \
  -F "file=@your-data.json" \
  -F "domain=制造业"
```

#### JSON数据处理
```bash
curl -X POST http://localhost:8080/api/process-mining/process \
  -H "Content-Type: application/json" \
  -d '{
    "jsonData": "your-json-string",
    "domain": "制造业",
    "outputFormat": "docx"
  }'
```

#### 查询任务状态
```bash
curl http://localhost:8080/api/process-mining/status/{taskId}
```

#### 下载报告
```bash
curl http://localhost:8080/api/process-mining/download/{taskId} -o report.docx
```

## 数据格式要求

输入的JSON数据应包含以下结构的模块：

```json
{
  "modules": [
    {
      "title": "模块标题",
      "type": "kpi_indicator|data_table|process_flow|variant_analysis",
      "prompt": "分析提示内容",
      "data": "模块数据",
      "kpi_value": "KPI值（可选）"
    }
  ]
}
```

支持的模块类型：
- `kpi_indicator`: KPI指标
- `data_table`: 数据表格
- `process_flow`: 流程图
- `variant_analysis`: 变体分析

## 项目结构

```
java-flow-mining-analyzer/
├── src/main/java/com/flowmining/
│   ├── FlowMiningAnalyzerApplication.java    # 主应用类
│   ├── config/                               # 配置类
│   │   ├── AIConfig.java                     # AI配置
│   │   └── AppConfig.java                    # 应用配置
│   ├── controller/                           # 控制器
│   │   └── ProcessMiningController.java      # REST API控制器
│   ├── model/                                # 数据模型
│   │   ├── Module.java                       # 模块模型
│   │   ├── TaskStatus.java                   # 任务状态模型
│   │   └── ProcessJsonRequest.java           # 请求模型
│   ├── service/                              # 业务服务
│   │   ├── JsonLoaderService.java            # JSON加载服务
│   │   ├── ModuleExtractorService.java       # 模块提取服务
│   │   ├── AIClientService.java              # AI客户端服务
│   │   ├── ContentGeneratorService.java      # 内容生成服务
│   │   ├── TitlePolisherService.java         # 标题润色服务
│   │   ├── DocumentBuilderService.java       # 文档构建服务
│   │   └── ReportProcessorService.java       # 报告处理服务
│   └── util/                                 # 工具类
│       ├── PromptTemplates.java              # 提示模板
│       ├── DataFormatter.java                # 数据格式化
│       └── FileUtils.java                    # 文件工具
├── src/main/resources/
│   ├── application.yml                       # 应用配置
│   └── static/
│       └── index.html                        # Web界面
└── pom.xml                                   # Maven配置
```

## 开发指南

### 添加新的模块类型

1. 在 `Module.java` 中添加新的模块类型枚举
2. 在 `ModuleExtractorService.java` 中添加识别逻辑
3. 在 `PromptTemplates.java` 中添加相应的提示模板
4. 在 `ContentGeneratorService.java` 中添加处理逻辑

### 自定义AI提示模板

编辑 `PromptTemplates.java` 文件中的模板方法，根据需要调整AI分析的角度和深度。

### 扩展文档格式

当前支持Word文档格式，可以通过扩展 `DocumentBuilderService.java` 来支持其他格式。

## 故障排除

### 常见问题

1. **AI API调用失败**
   - 检查API密钥是否正确配置
   - 确认网络连接正常
   - 查看日志中的详细错误信息

2. **文件上传失败**
   - 检查文件大小是否超过50MB限制
   - 确认文件格式为JSON
   - 检查磁盘空间是否充足

3. **报告生成失败**
   - 检查输出目录权限
   - 确认JSON数据格式正确
   - 查看应用日志获取详细错误信息

### 日志查看

应用日志保存在 `logs/flow-mining-analyzer.log` 文件中，可以通过以下命令查看：

```bash
tail -f logs/flow-mining-analyzer.log
```

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
