2025-07-07 16:46:35 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 30400 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 16:46:35 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:46:35 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07 16:46:35 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07 16:46:35 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-07 16:46:35 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:46:35 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 16:46:35 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:46:35 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 657 ms
2025-07-07 16:46:36 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:46:36 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-07 16:46:36 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 16:46:36 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-07-07 16:46:36 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 16:46:36 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 16:46:36 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-07 16:47:18 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 32220 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 16:47:18 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:47:18 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07 16:47:18 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07 16:47:19 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 16:47:19 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:47:19 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 16:47:19 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:47:19 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 424 ms
2025-07-07 16:47:19 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:47:19 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-07 16:47:19 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 16:47:19 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-07 16:47:19 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Started FlowMiningAnalyzerApplication in 0.842 seconds (JVM running for 1.001)
2025-07-07 16:47:28 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 16:47:28 [http-nio-8081-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 16:47:28 [http-nio-8081-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-07 16:48:17 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 34539 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 16:48:17 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:48:17 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07 16:48:17 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07 16:48:17 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 16:48:17 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:48:17 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 16:48:17 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:48:17 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 497 ms
2025-07-07 16:48:17 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:48:17 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-07 16:48:17 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 16:48:18 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-07 16:48:18 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Started FlowMiningAnalyzerApplication in 5.984 seconds (JVM running for 6.157)
2025-07-07 16:48:24 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 16:48:24 [http-nio-8081-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 16:48:24 [http-nio-8081-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-07 16:51:27 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 41921 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 16:51:27 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:51:27 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07 16:51:27 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07 16:51:27 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 16:51:27 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:51:27 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 16:51:27 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:51:27 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 443 ms
2025-07-07 16:51:27 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:51:28 [restartedMain] WARN  o.s.b.d.a.OptionalLiveReloadServer - Unable to start LiveReload server
2025-07-07 16:51:28 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 16:51:28 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-07 16:51:28 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 16:51:28 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 16:51:28 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-07 16:51:37 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 42667 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 16:51:37 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:51:37 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07 16:51:37 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07 16:51:38 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 16:51:38 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:51:38 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 16:51:38 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:51:38 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 408 ms
2025-07-07 16:51:38 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:51:38 [restartedMain] WARN  o.s.b.d.a.OptionalLiveReloadServer - Unable to start LiveReload server
2025-07-07 16:51:38 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 16:51:38 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-07 16:51:38 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 16:51:38 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 16:51:38 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-07 16:55:49 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 54855 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 16:55:49 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 16:55:49 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07 16:55:49 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07 16:55:50 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 16:55:50 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 16:55:50 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 16:55:50 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 16:55:50 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 495 ms
2025-07-07 16:55:50 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:55:50 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-07 16:55:50 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 16:55:50 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-07 16:55:50 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Started FlowMiningAnalyzerApplication in 5.992 seconds (JVM running for 6.172)
2025-07-07 16:55:50 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 16:55:50 [http-nio-8081-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 16:55:50 [http-nio-8081-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-07 17:00:35 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 12 class path changes (8 additions, 0 deletions, 4 modifications)
2025-07-07 17:00:36 [Thread-6] WARN  o.apache.tomcat.util.net.NioEndpoint - Failed to unlock acceptor for [http-nio-8081] because the local address was not available
2025-07-07 17:00:36 [Thread-6] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 17:00:36 [Thread-6] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 17:00:36 [http-nio-8081-Acceptor] ERROR org.apache.tomcat.util.net.Acceptor - Socket accept failed
java.nio.channels.AsynchronousCloseException: null
	at java.nio.channels.spi.AbstractInterruptibleChannel.end(AbstractInterruptibleChannel.java:205)
	at sun.nio.ch.ServerSocketChannelImpl.accept(ServerSocketChannelImpl.java:256)
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:548)
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:79)
	at org.apache.tomcat.util.net.Acceptor.run(Acceptor.java:129)
	at java.lang.Thread.run(Thread.java:750)
2025-07-07 17:00:41 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 54855 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 17:00:41 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 17:00:42 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 17:00:42 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 17:00:42 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 17:00:42 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 17:00:42 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 129 ms
2025-07-07 17:00:42 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 17:00:42 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-07 17:00:42 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 17:00:42 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-07 17:00:42 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Started FlowMiningAnalyzerApplication in 5.259 seconds (JVM running for 297.673)
2025-07-07 17:00:42 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation delta:


==========================
CONDITION EVALUATION DELTA
==========================


Positive matches:
-----------------

    None


Negative matches:
-----------------

   TaskExecutionAutoConfiguration#applicationTaskExecutor:
      Did not match:
         - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) found beans of type 'java.util.concurrent.Executor' taskExecutor (OnBeanCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    None



2025-07-07 17:01:13 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 12 class path changes (0 additions, 5 deletions, 7 modifications)
2025-07-07 17:01:14 [Thread-8] WARN  o.apache.tomcat.util.net.NioEndpoint - Failed to unlock acceptor for [http-nio-8081] because the local address was not available
2025-07-07 17:01:14 [Thread-8] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 17:01:14 [http-nio-8081-Acceptor] ERROR org.apache.tomcat.util.net.Acceptor - Socket accept failed
java.nio.channels.AsynchronousCloseException: null
	at java.nio.channels.spi.AbstractInterruptibleChannel.end(AbstractInterruptibleChannel.java:205) ~[na:1.8.0_412]
	at sun.nio.ch.ServerSocketChannelImpl.accept(ServerSocketChannelImpl.java:256) ~[na:1.8.0_412]
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:548) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:79) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.Acceptor.run(Acceptor.java:129) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_412]
2025-07-07 17:01:19 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 54855 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 17:01:19 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 17:01:19 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 17:01:19 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 17:01:19 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 17:01:19 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 17:01:19 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 112 ms
2025-07-07 17:01:19 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 17:01:19 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-07 17:01:19 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 17:01:19 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-07 17:01:19 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Started FlowMiningAnalyzerApplication in 5.224 seconds (JVM running for 335.092)
2025-07-07 17:01:19 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation delta:


==========================
CONDITION EVALUATION DELTA
==========================


Positive matches:
-----------------

   TaskExecutionAutoConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

    None


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    None



2025-07-07 17:02:20 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 7 class path changes (0 additions, 0 deletions, 7 modifications)
2025-07-07 17:02:21 [Thread-12] WARN  o.apache.tomcat.util.net.NioEndpoint - Failed to unlock acceptor for [http-nio-8081] because the local address was not available
2025-07-07 17:02:21 [Thread-12] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 17:02:21 [http-nio-8081-Acceptor] ERROR org.apache.tomcat.util.net.Acceptor - Socket accept failed
java.nio.channels.AsynchronousCloseException: null
	at java.nio.channels.spi.AbstractInterruptibleChannel.end(AbstractInterruptibleChannel.java:205) ~[na:1.8.0_412]
	at sun.nio.ch.ServerSocketChannelImpl.accept(ServerSocketChannelImpl.java:256) ~[na:1.8.0_412]
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:548) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:79) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.Acceptor.run(Acceptor.java:129) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_412]
2025-07-07 17:02:26 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 54855 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 17:02:26 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 17:02:26 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 17:02:26 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 17:02:26 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 17:02:26 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 17:02:26 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 97 ms
2025-07-07 17:02:26 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 17:02:26 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-07 17:02:26 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 17:02:26 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-07 17:02:26 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Started FlowMiningAnalyzerApplication in 5.196 seconds (JVM running for 402.282)
2025-07-07 17:02:26 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-07-07 17:02:31 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 71397 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 17:02:31 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 17:02:31 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07 17:02:31 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07 17:02:32 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 17:02:32 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 17:02:32 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 17:02:32 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 17:02:32 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 408 ms
2025-07-07 17:02:32 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 17:02:32 [restartedMain] WARN  o.s.b.d.a.OptionalLiveReloadServer - Unable to start LiveReload server
2025-07-07 17:02:32 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 17:02:32 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-07 17:02:32 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 17:02:32 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 17:02:32 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-07 17:03:04 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 72641 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 17:03:04 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 17:03:04 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07 17:03:04 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07 17:03:04 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 17:03:04 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 17:03:04 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 17:03:04 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 17:03:04 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 384 ms
2025-07-07 17:03:04 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 17:03:04 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-07 17:03:04 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 17:03:04 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-07 17:03:04 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Started FlowMiningAnalyzerApplication in 5.822 seconds (JVM running for 5.968)
2025-07-07 17:03:13 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 17:03:13 [http-nio-8081-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 17:03:13 [http-nio-8081-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-07 17:05:49 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 79265 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 17:05:49 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 17:05:49 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07 17:05:49 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07 17:05:49 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 17:05:49 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 17:05:49 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 17:05:49 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 17:05:49 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 423 ms
2025-07-07 17:05:49 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 17:05:50 [restartedMain] WARN  o.s.b.d.a.OptionalLiveReloadServer - Unable to start LiveReload server
2025-07-07 17:05:50 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 17:05:50 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-07-07 17:05:50 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-07 17:05:50 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-07 17:05:50 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-07 17:06:06 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication using Java 1.8.0_412 on Mac with PID 80082 (/Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer/target/classes started by jeffpan in /Users/<USER>/Documents/shangping_develop/flow_mining_analyzer/java-flow-mining-analyzer)
2025-07-07 17:06:06 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-07 17:06:07 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-07 17:06:07 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-07 17:06:07 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8081 (http)
2025-07-07 17:06:07 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-07 17:06:07 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-07 17:06:07 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-07 17:06:07 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 387 ms
2025-07-07 17:06:07 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 17:06:07 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-07 17:06:07 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-07 17:06:07 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path ''
2025-07-07 17:06:07 [restartedMain] INFO  c.f.FlowMiningAnalyzerApplication - Started FlowMiningAnalyzerApplication in 5.821 seconds (JVM running for 5.971)
2025-07-07 17:06:26 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 17:06:26 [http-nio-8081-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 17:06:26 [http-nio-8081-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
