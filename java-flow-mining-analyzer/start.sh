#!/bin/bash

# 流程挖掘分析报告生成器启动脚本

echo "=========================================="
echo "流程挖掘分析报告生成器 (Java版本)"
echo "=========================================="

# 检查Java版本
echo "检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请安装Java 17或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "错误: Java版本过低，需要Java 17或更高版本，当前版本: $JAVA_VERSION"
    exit 1
fi

echo "Java版本检查通过: $(java -version 2>&1 | head -n 1)"

# 检查Maven
echo "检查Maven环境..."
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven，请安装Maven 3.6或更高版本"
    exit 1
fi

echo "Maven版本: $(mvn -version | head -n 1)"

# 检查环境变量
echo "检查环境变量..."
if [ -z "$AI_API_KEY" ]; then
    echo "警告: 未设置AI_API_KEY环境变量"
    echo "请设置通义千问API密钥: export AI_API_KEY=your-api-key"
    echo "或在application.yml中配置"
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p output
mkdir -p uploads
mkdir -p temp
mkdir -p logs

# 编译项目
echo "编译项目..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo "错误: 项目编译失败"
    exit 1
fi

echo "编译成功!"

# 启动应用
echo "启动应用..."
echo "访问地址: http://localhost:8080"
echo "按 Ctrl+C 停止应用"
echo "=========================================="

mvn spring-boot:run
