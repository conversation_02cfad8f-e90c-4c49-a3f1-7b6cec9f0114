server:
  port: 8081
  servlet:
    context-path: /
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

spring:
  application:
    name: flow-mining-analyzer
  
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 50MB
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
  
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# AI APIÅäÖÃ
#  API_KEY = os.getenv("DASHSCOPE_API_KEY", "sk-eddb8e0cf8c14e48a8dc4018a988591e")
#  BASE_URL = os.getenv("DASHSCOPE_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
#  MODEL_NAME = os.getenv("AI_MODEL_NAME", "qwen-max-2025-01-25")

ai:
  api-key: ${AI_API_KEY:sk-eddb8e0cf8c14e48a8dc4018a988591e}
  base-url: ${AI_BASE_URL:https://dashscope.aliyuncs.com/compatible-mode/v1}
  model: ${AI_MODEL:qwen-max-2025-01-25}
  max-tokens: ${AI_MAX_TOKENS:2000}
  temperature: ${AI_TEMPERATURE:0.7}
  timeout-seconds: ${AI_TIMEOUT:30}
  max-retries: ${AI_MAX_RETRIES:3}
  rate-limit-requests-per-minute: ${AI_RATE_LIMIT:60}

app:
  output-dir: ${APP_OUTPUT_DIR:./output}
  upload-dir: ${APP_UPLOAD_DIR:./uploads}
  temp-dir: ${APP_TEMP_DIR:./temp}
  cleanup-enabled: ${APP_CLEANUP_ENABLED:true}
  cleanup-interval-hours: ${APP_CLEANUP_INTERVAL:24}
  temp-file-max-age-hours: ${APP_TEMP_FILE_MAX_AGE:72}

logging:
  level:
    com.flowmining: INFO
    org.springframework.web: INFO
    org.apache.poi: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/flow-mining-analyzer.log
    max-size: 10MB
    max-history: 30

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

async:
  core-pool-size: 2
  max-pool-size: 10
  queue-capacity: 100
  thread-name-prefix: "async-"
