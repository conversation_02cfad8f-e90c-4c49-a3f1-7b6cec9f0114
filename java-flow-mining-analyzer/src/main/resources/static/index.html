<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flow Mining Analyzer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .upload-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }

        .upload-icon {
            font-size: 3em;
            color: #ddd;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #666;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }



        .status {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .download-link {
            display: inline-block;
            margin-top: 10px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .download-link:hover {
            text-decoration: underline;
        }

        .hidden {
            display: none !important;
        }

        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Flow Mining Analyzer</h1>
            <p>AI-Powered Process Mining Report Generator</p>
        </div>
        
        <div class="content">
            <div class="upload-section">
                <h2 class="section-title">Upload JSON Data File</h2>
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        Click to select file or drag file here<br>
                        <small>Support JSON format, max 50MB</small>
                    </div>
                </div>
                <input type="file" id="fileInput" accept=".json" />
            </div>

            <div class="form-group">
                <label class="form-label" for="domainInput">Business Domain (Optional)</label>
                <input type="text" id="domainInput" class="form-input"
                       placeholder="e.g., Banking, Healthcare, Manufacturing" />
            </div>

            <button class="btn" id="uploadBtn" disabled>
                Start Analysis
            </button>



            <div class="status" id="status"></div>
        </div>
    </div>

    <script>
        let selectedFile = null;

        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        const domainInput = document.getElementById('domainInput');
        const status = document.getElementById('status');

        // File selection events
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFileSelect);

        // Drag events
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // Upload button event
        uploadBtn.addEventListener('click', uploadFile);

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.name.toLowerCase().endsWith('.json')) {
                showStatus('error', 'Please select a JSON format file');
                return;
            }

            if (file.size > 50 * 1024 * 1024) {
                showStatus('error', 'File size cannot exceed 50MB');
                return;
            }

            selectedFile = file;
            uploadArea.innerHTML = `
                <div class="upload-icon">📁</div>
                <div class="upload-text">
                    Selected file: ${file.name}<br>
                    <small>File size: ${formatFileSize(file.size)}</small>
                </div>
            `;
            uploadBtn.disabled = false;
            hideStatus();
        }

        async function uploadFile() {
            if (!selectedFile) {
                showStatus('error', 'Please select a file');
                return;
            }

            showStatus('success', 'File uploaded successfully! Analysis feature coming soon...');
        }

        function showStatus(type, message) {
            status.className = `status ${type}`;
            status.innerHTML = message;
            status.style.display = 'block';
        }

        function hideStatus() {
            status.style.display = 'none';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
