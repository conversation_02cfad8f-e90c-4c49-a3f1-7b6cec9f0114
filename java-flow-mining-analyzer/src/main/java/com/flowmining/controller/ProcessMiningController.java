package com.flowmining.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.ResponseEntity;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/process-mining")
public class ProcessMiningController {

    @GetMapping("/health")
    public String health() {
        return "OK";
    }

    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "domain", required = false) String domain) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Basic validation
            if (file.isEmpty()) {
                response.put("success", false);
                response.put("message", "File is empty");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (!file.getOriginalFilename().toLowerCase().endsWith(".json")) {
                response.put("success", false);
                response.put("message", "Only JSON files are supported");
                return ResponseEntity.badRequest().body(response);
            }
            
            // Generate task ID
            String taskId = UUID.randomUUID().toString();
            
            response.put("success", true);
            response.put("taskId", taskId);
            response.put("message", "File uploaded successfully, analysis started");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Upload failed: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/status/{taskId}")
    public ResponseEntity<Map<String, Object>> getTaskStatus(@PathVariable String taskId) {
        Map<String, Object> response = new HashMap<>();
        
        // Simulate task status
        response.put("taskId", taskId);
        response.put("status", "COMPLETED");
        response.put("progress", 100);
        response.put("message", "Analysis completed successfully");
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/download/{taskId}")
    public ResponseEntity<String> downloadReport(@PathVariable String taskId) {
        // Simulate download - return a message for now
        return ResponseEntity.ok("Report download feature will be implemented soon. Task ID: " + taskId);
    }
}
