package com.flowmining.model;

import java.time.LocalDateTime;

public class AnalysisTask {
    private String taskId;
    private String fileName;
    private String domain;
    private TaskStatus status;
    private int progress;
    private String reportPath;
    private String errorMessage;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    public AnalysisTask() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.status = TaskStatus.PENDING;
        this.progress = 0;
    }

    // Getters and Setters
    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }

    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }

    public String getDomain() { return domain; }
    public void setDomain(String domain) { this.domain = domain; }

    public TaskStatus getStatus() { return status; }
    public void setStatus(TaskStatus status) { 
        this.status = status; 
        this.updateTime = LocalDateTime.now();
    }

    public int getProgress() { return progress; }
    public void setProgress(int progress) { 
        this.progress = progress; 
        this.updateTime = LocalDateTime.now();
    }

    public String getReportPath() { return reportPath; }
    public void setReportPath(String reportPath) { this.reportPath = reportPath; }

    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }

    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
}
