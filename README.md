# 流程挖掘分析报告生成器

这是一个流程挖掘分析报告生成工具，能够基于JSON数据自动生成专业的流程分析报告Word文档。

## 功能特点

- 自动处理各种格式的JSON数据，提取流程相关信息
- 使用AI模型（Qwen-plus）生成专业的分析内容
- 润色标题，使报告更加专业
- 自动生成Word格式的分析报告
- 支持常规模块和简单KPI指标模块的处理

## 安装



1. 安装依赖：
```bash
pip3 install -r requirements.txt
```

2. 配置API密钥（可选）：
   - 创建 `.env` 文件并添加您的API密钥：
   ```
   DASHSCOPE_API_KEY=your_api_key_here
   ```

## 使用方法

基本用法：

```bash
python api_server.py 可视化界面，可以自行配置。
```

