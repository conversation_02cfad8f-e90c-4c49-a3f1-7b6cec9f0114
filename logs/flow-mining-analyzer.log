2025-07-29 09:33:20 [main] INFO  c.f.FlowMiningAnalyzerApplication - Starting FlowMiningAnalyzerApplication v1.0.0 using Java 17.0.15 on 00d58da00f48 with PID 7 (/app/app.jar started by root in /app)
2025-07-29 09:33:20 [main] INFO  c.f.FlowMiningAnalyzerApplication - The following 1 profile is active: "docker"
2025-07-29 09:33:20 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-29 09:33:20 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 09:33:20 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-29 09:33:20 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 09:33:20 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 524 ms
2025-07-29 09:33:21 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-29 09:33:21 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-29 09:33:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-29 09:33:21 [main] INFO  c.f.FlowMiningAnalyzerApplication - Started FlowMiningAnalyzerApplication in 1.102 seconds (JVM running for 1.301)
2025-07-29 09:33:40 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 09:33:40 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 09:33:40 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
