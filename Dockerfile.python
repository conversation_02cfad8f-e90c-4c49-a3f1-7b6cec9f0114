# 使用官方Python 3.10镜像作为基础镜像
# FROM docker.mirrors.ustc.edu.cn/library/python:3.10-slim
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        gcc \
        g++ \
        libxml2-dev \
        libxslt-dev \
        libffi-dev \
        libssl-dev \
        build-essential \
        python3-dev \
        curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 复制wheels目录和requirements文件
COPY wheels_linux/ ./wheels_linux/
COPY requirements.txt .

# 安装Python依赖（使用本地wheels）
RUN pip install --upgrade pip && \
    pip install --find-links ./wheels_linux --no-index -r requirements.txt || \
    pip install -r requirements.txt

# 复制项目文件
COPY . .

# 创建必要的目录并设置权限
RUN mkdir -p /app/uploads /app/outputs /app/temp /app/logs /app/api/uploads /app/api/outputs /app/api/temp && \
    chmod 755 /app/uploads /app/outputs /app/temp /app/logs /app/api/uploads /app/api/outputs /app/api/temp

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 设置容器启动时的默认命令 - 启动Web API服务器
CMD ["python", "api/api_server.py"]
