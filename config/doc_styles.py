from docx.shared import Pt, RGBColor, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

# 文档标题样式
TITLE_STYLE = {
    "font_name": "Arial",
    "font_size": Pt(24),
    "bold": True,
    "color": RGBColor(0, 51, 102),
    "alignment": WD_ALIGN_PARAGRAPH.CENTER,
    "space_after": Pt(24)
}

# 部分标题样式
HEADING1_STYLE = {
    "font_name": "Arial",
    "font_size": Pt(18),
    "bold": True,
    "color": RGBColor(0, 51, 102),
    "space_before": Pt(24),
    "space_after": Pt(12)
}

HEADING2_STYLE = {
    "font_name": "Arial",
    "font_size": Pt(16),
    "bold": True,
    "color": RGBColor(0, 51, 102),
    "space_before": Pt(18),
    "space_after": Pt(8)
}

HEADING3_STYLE = {
    "font_name": "Arial",
    "font_size": Pt(14),
    "bold": True,
    "color": RGBColor(0, 51, 102),
    "space_before": Pt(12),
    "space_after": Pt(6)
}

# 正文样式
PARAGRAPH_STYLE = {
    "font_name": "Arial",
    "font_size": Pt(11),
    "line_spacing": 1.15,
    "space_after": Pt(8)
}

# 表格样式
TABLE_STYLE = {
    "header_bg_color": RGBColor(217, 226, 243),
    "header_font_color": RGBColor(0, 51, 102),
    "header_font_bold": True,
    "border_size": Pt(1),
    "border_color": RGBColor(169, 169, 169)
}

# 页面设置
PAGE_SETTINGS = {
    "top_margin": Inches(1),
    "bottom_margin": Inches(1),
    "left_margin": Inches(1),
    "right_margin": Inches(1)
}

# KPI显示样式（用于simple模块）
KPI_STYLE = {
    "font_name": "Arial",
    "font_size": Pt(12),
    "bold": True,
    "color": RGBColor(0, 102, 0)
}