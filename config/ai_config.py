import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# AI API配置
API_KEY = os.getenv("DASHSCOPE_API_KEY", "sk-eddb8e0cf8c14e48a8dc4018a988591e")
BASE_URL = os.getenv("DASHSCOPE_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
MODEL_NAME = os.getenv("AI_MODEL_NAME", "qwen-max-2025-01-25")

# 生成参数配置
DEFAULT_MAX_TOKENS = 2048
DEFAULT_TEMPERATURE = 0.7
DEFAULT_DOMAIN = None  # 默认不使用特定领域，保持通用
DEFAULT_DOMAIN = "分结合下面所有的数据进行深度分析
并给出3条结论，结论中要有数据支撑，给出改善的意见"  # 默认不使用特定领域，保持通用

# 使用频率限制
REQUEST_INTERVAL = 0.5  # 请求间隔(秒)

# 多模型配置（用于不同任务）
MODELS = {
    "title": {
        "name": MODEL_NAME,
        "max_tokens": 100,
        "temperature": 0.7
    },
    "content": {
        "name": MODEL_NAME,
        "max_tokens": 1500,
        "temperature": 0.7
    },
    "summary": {
        "name": MODEL_NAME,
        "max_tokens": 500,
        "temperature": 0.6
    }
}

# 错误重试配置
MAX_RETRIES = 3
RETRY_DELAY = 2  # 秒