import os
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 项目路径
BASE_DIR = Path(__file__).resolve().parent.parent

# 应用基本配置
APP_NAME = "流程挖掘分析报告生成器"
APP_VERSION = "1.0.0"

# 输出配置
OUTPUT_DIR = os.getenv("OUTPUT_DIR", os.path.join(BASE_DIR, "output"))
DEFAULT_OUTPUT_FILENAME = "流程挖掘分析报告.docx"

# 日志配置
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FILE = os.path.join(BASE_DIR, "logs", "app.log")

# 临时文件配置
TEMP_DIR = os.path.join(BASE_DIR, "temp")