# Flow Mining Analyzer Docker 部署指南

## 快速开始

### 1. 使用部署脚本（推荐）
```bash
./deploy.sh
```

### 2. 手动部署

#### 构建镜像
```bash
# 在线构建
docker build -t flow-mining-analyzer:latest .

# 离线构建（使用本地wheel包）
docker build -f Dockerfile.offline -t flow-mining-analyzer:latest .
```

#### 启动服务
```bash
# 使用 docker-compose
docker-compose up -d

# 或使用新版 docker compose
docker compose up -d
```

## 环境变量配置

创建 `.env` 文件设置环境变量：
```bash
# AI服务API密钥（必需）
DASHSCOPE_API_KEY=your_dashscope_api_key

# 输出目录（可选）
OUTPUT_DIR=/app/outputs

# 日志级别（可选）
LOG_LEVEL=INFO
```

## 服务访问

- **Web界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## 数据卷

| 本地目录 | 容器目录 | 说明 |
|---------|----------|------|
| `./uploads` | `/app/uploads` | JSON文件上传目录 |
| `./outputs` | `/app/outputs` | 生成的Word文档输出目录 |
| `./logs` | `/app/logs` | 应用日志目录 |
| `./api/uploads` | `/app/api/uploads` | API上传临时目录 |
| `./api/outputs` | `/app/api/outputs` | API输出目录 |

## 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker logs flow-mining-analyzer

# 实时查看日志
docker logs -f flow-mining-analyzer

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建并启动
docker-compose up -d --build

# 进入容器
docker exec -it flow-mining-analyzer bash
```

## 生产环境部署

### 使用Nginx代理
```bash
# 启动包含nginx的完整栈
docker-compose --profile production up -d
```

### 配置SSL证书
1. 将SSL证书放在 `nginx/ssl/` 目录
2. 修改 `nginx/nginx.conf` 配置
3. 重启nginx服务

## 故障排除

### 1. 容器无法启动
```bash
# 检查容器日志
docker logs flow-mining-analyzer

# 检查容器状态
docker inspect flow-mining-analyzer
```

### 2. API密钥问题
确保设置了正确的 `DASHSCOPE_API_KEY` 环境变量：
```bash
export DASHSCOPE_API_KEY=your_api_key
```

### 3. 端口冲突
如果8000端口被占用，修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "8080:8000"  # 将本地端口改为8080
```

### 4. 文件权限问题
```bash
# 修复目录权限
sudo chown -R $(id -u):$(id -g) uploads outputs logs
```

## 架构说明

### 容器架构
- **基础镜像**: python:3.10-slim
- **工作目录**: /app
- **暴露端口**: 8000
- **健康检查**: /health 端点

### 网络配置
- **网络名称**: flow-mining-network
- **网络类型**: bridge
- **服务间通信**: 容器名称解析

### 数据持久化
- 上传文件和输出文件通过数据卷持久化
- 日志文件映射到宿主机便于查看
- 临时文件在容器内处理

## 性能优化

### 1. 构建优化
- 使用多阶段构建减少镜像大小
- 利用Docker层缓存加速构建
- 通过.dockerignore排除不必要文件

### 2. 运行时优化
- 设置合适的内存和CPU限制
- 使用健康检查确保服务可用性
- 配置日志轮转避免磁盘空间不足

### 3. 网络优化
- 使用nginx反向代理提升性能
- 配置gzip压缩减少传输数据
- 设置适当的超时时间