[{"variant": {"variantList": [{"varName": "#1", "value": 9.34, "count": 63569, "variant": "手机银行：存款", "pathKpiValue": "157.07911088738223", "variantMd5": "5omL5py66ZO26KGM77ya5a2Y5qy+", "eventList": "手机银行：存款", "eventSize": 1, "caseDuration": 157.07911088738223, "skip": null, "valueList": [{"value": "所占比例:9.34%"}, {"value": "案例数量:63569"}]}, {"varName": "#2", "value": 3.38, "count": 23026, "variant": "手机银行：理财,手机银行：存款,手机银行：理财", "pathKpiValue": "281.24463649787197", "variantMd5": "5omL5py66ZO26KGM77ya55CG6LSiLOaJi+acuumTtuihjO+8muWtmOasvizmiYvmnLrpk7booYzv\r\nvJrnkIbotKI=", "eventList": "手机银行：理财,手机银行：存款,手机银行：理财", "eventSize": 3, "caseDuration": 281.24463649787197, "skip": null, "valueList": [{"value": "所占比例:3.38%"}, {"value": "案例数量:23026"}]}, {"varName": "#3", "value": 3.22, "count": 21898, "variant": "手机银行：月度账单", "pathKpiValue": "98.91012877888392", "variantMd5": "5omL5py66ZO26KGM77ya5pyI5bqm6LSm5Y2V", "eventList": "手机银行：月度账单", "eventSize": 1, "caseDuration": 98.91012877888392, "skip": null, "valueList": [{"value": "所占比例:3.22%"}, {"value": "案例数量:21898"}]}, {"varName": "#4", "value": 3.08, "count": 20951, "variant": "手机银行：存款,手机银行：理财", "pathKpiValue": "361.64607894611237", "variantMd5": "5omL5py66ZO26KGM77ya5a2Y5qy+LOaJi+acuumTtuihjO+8mueQhui0og==", "eventList": "手机银行：存款,手机银行：理财", "eventSize": 2, "caseDuration": 361.64607894611237, "skip": null, "valueList": [{"value": "所占比例:3.08%"}, {"value": "案例数量:20951"}]}, {"varName": "#5", "value": 3.05, "count": 20803, "variant": "手机银行：理财,手机银行：存款", "pathKpiValue": "219.54040282651542", "variantMd5": "5omL5py66ZO26KGM77ya55CG6LSiLOaJi+acuumTtuihjO+8muWtmOasvg==", "eventList": "手机银行：理财,手机银行：存款", "eventSize": 2, "caseDuration": 219.54040282651542, "skip": null, "valueList": [{"value": "所占比例:3.05%"}, {"value": "案例数量:20803"}]}, {"varName": "#6", "value": 2.8, "count": 19105, "variant": "发送站内消息", "pathKpiValue": "3206427.292384193", "variantMd5": "5Y+R6YCB56uZ5YaF5raI5oGv", "eventList": "发送站内消息", "eventSize": 1, "caseDuration": 3206427.292384193, "skip": null, "valueList": [{"value": "所占比例:2.8%"}, {"value": "案例数量:19105"}]}, {"varName": "#7", "value": 2.78, "count": 18925, "variant": "手机银行：信用卡,手机银行：存款,手机银行：理财", "pathKpiValue": "265.5468956406869", "variantMd5": "5omL5py66ZO26KGM77ya5L+h55So5Y2hLOaJi+acuumTtuihjO+8muWtmOasvizmiYvmnLrpk7bo\r\noYzvvJrnkIbotKI=", "eventList": "手机银行：信用卡,手机银行：存款,手机银行：理财", "eventSize": 3, "caseDuration": 265.5468956406869, "skip": null, "valueList": [{"value": "所占比例:2.78%"}, {"value": "案例数量:18925"}]}, {"varName": "#8", "value": 2.31, "count": 15715, "variant": "手机银行：信用卡,手机银行：存款", "pathKpiValue": "218.8390709513204", "variantMd5": "5omL5py66ZO26KGM77ya5L+h55So5Y2hLOaJi+acuumTtuihjO+8muWtmOasvg==", "eventList": "手机银行：信用卡,手机银行：存款", "eventSize": 2, "caseDuration": 218.8390709513204, "skip": null, "valueList": [{"value": "所占比例:2.31%"}, {"value": "案例数量:15715"}]}, {"varName": "#9", "value": 1.57, "count": 10736, "variant": "手机银行：转账汇款", "pathKpiValue": "276.7943368107303", "variantMd5": "5omL5py66ZO26KGM77ya6L2s6LSm5rGH5qy+", "eventList": "手机银行：转账汇款", "eventSize": 1, "caseDuration": 276.7943368107303, "skip": null, "valueList": [{"value": "所占比例:1.57%"}, {"value": "案例数量:10736"}]}, {"varName": "#10", "value": 1.13, "count": 7699, "variant": "手机银行：月度账单,手机银行：理财", "pathKpiValue": "286.5569554487596", "variantMd5": "5omL5py66ZO26KGM77ya5pyI5bqm6LSm5Y2VLOaJi+acuumTtuihjO+8mueQhui0og==", "eventList": "手机银行：月度账单,手机银行：理财", "eventSize": 2, "caseDuration": 286.5569554487596, "skip": null, "valueList": [{"value": "所占比例:1.13%"}, {"value": "案例数量:7699"}]}, {"varName": "其他", "value": 67.29, "count": 457579, "variant": "其他", "pathKpiValue": "735.8865572939318", "variantMd5": "其他", "eventList": "其他", "eventSize": 1, "caseDuration": 735.8865572939318, "skip": null, "valueList": [{"value": "所占比例:67.29%"}, {"value": "案例数量:457579"}]}], "variantCalculate": {"variantNum": 0, "variantCount": 114607, "caseCount": 680006, "caseRate": 0}, "currentEventColumn": "ACTIVITY_COLUMN()", "defaultEventColumn": "ACTIVITY_COLUMN()", "errorMsg": null, "pathKpis": null}, "treeChart": {"eventNodes": [{"gradient": 0, "name": "手机银行：月度账单", "number": 29597, "second": "0.0秒", "color": "", "kpiValues": null, "isStart": false, "isEnd": false, "eventRatio": null, "eventCaseRatio": null, "occursOnAveragePerCase": null, "start": false, "end": false}, {"gradient": 0, "name": "手机银行：信用卡", "number": 34640, "second": "0.0秒", "color": "", "kpiValues": null, "isStart": false, "isEnd": false, "eventRatio": null, "eventCaseRatio": null, "occursOnAveragePerCase": null, "start": false, "end": false}, {"gradient": 0, "name": "手机银行：理财", "number": 91404, "second": "0.0秒", "color": "", "kpiValues": null, "isStart": false, "isEnd": false, "eventRatio": null, "eventCaseRatio": null, "occursOnAveragePerCase": null, "start": false, "end": false}, {"gradient": 0, "name": "*开始*", "number": 222427, "second": "0.0秒", "color": "", "kpiValues": null, "isStart": true, "isEnd": false, "eventRatio": null, "eventCaseRatio": null, "occursOnAveragePerCase": null, "start": true, "end": false}, {"gradient": 0, "name": "手机银行：存款", "number": 162989, "second": "0.0秒", "color": "", "kpiValues": null, "isStart": false, "isEnd": false, "eventRatio": null, "eventCaseRatio": null, "occursOnAveragePerCase": null, "start": false, "end": false}, {"gradient": 0, "name": "发送站内消息", "number": 19105, "second": "0.0秒", "color": "", "kpiValues": null, "isStart": false, "isEnd": false, "eventRatio": null, "eventCaseRatio": null, "occursOnAveragePerCase": null, "start": false, "end": false}, {"gradient": 0, "name": "*结束*", "number": 222427, "second": "0.0秒", "color": "", "kpiValues": null, "isStart": false, "isEnd": true, "eventRatio": null, "eventCaseRatio": null, "occursOnAveragePerCase": null, "start": false, "end": true}, {"gradient": 0, "name": "手机银行：转账汇款", "number": 10736, "second": "0.0秒", "color": "", "kpiValues": null, "isStart": false, "isEnd": false, "eventRatio": null, "eventCaseRatio": null, "occursOnAveragePerCase": null, "start": false, "end": false}], "lineNodes": [{"source": "手机银行：月度账单", "target": "手机银行：理财", "second": "276.9秒", "number": null, "penWidth": 0.441069352774115, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": false, "gradient": null, "kpiValues": [], "startOrEnd": false, "showSecond": "276.9秒"}, {"source": "手机银行：存款", "target": "手机银行：理财", "second": "273.4秒", "number": null, "penWidth": 0.4355116048947954, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": false, "gradient": null, "kpiValues": [], "startOrEnd": false, "showSecond": "273.4秒"}, {"source": "手机银行：信用卡", "target": "手机银行：存款", "second": "33.4秒", "number": null, "penWidth": 0.05326002747708874, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": false, "gradient": null, "kpiValues": [], "startOrEnd": false, "showSecond": "33.4秒"}, {"source": "手机银行：理财", "target": "手机银行：存款", "second": "33.0秒", "number": null, "penWidth": 0.052635235829076386, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": false, "gradient": null, "kpiValues": [], "startOrEnd": false, "showSecond": "33.0秒"}, {"source": "*开始*", "target": "发送站内消息", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}, {"source": "发送站内消息", "target": "*结束*", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}, {"source": "*开始*", "target": "手机银行：存款", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}, {"source": "手机银行：存款", "target": "*结束*", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}, {"source": "*开始*", "target": "手机银行：信用卡", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}, {"source": "手机银行：理财", "target": "*结束*", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}, {"source": "*开始*", "target": "手机银行：月度账单", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}, {"source": "手机银行：月度账单", "target": "*结束*", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}, {"source": "*开始*", "target": "手机银行：理财", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}, {"source": "*开始*", "target": "手机银行：转账汇款", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}, {"source": "手机银行：转账汇款", "target": "*结束*", "second": "", "number": null, "penWidth": 0.001593070820447678, "color": "", "defaultLine": null, "lineCaseRatio": null, "isStartOrEnd": true, "gradient": null, "kpiValues": [], "startOrEnd": true, "showSecond": ""}], "maxLines": 500, "maxEvents": 500, "eventKpis": null, "lineKpis": null, "limitEnabled": false, "errorMsg": null}, "valueShowType": 1, "checkList": ["5omL5py66ZO26KGM77ya5a2Y5qy+", "5omL5py66ZO26KGM77ya55CG6LSiLOaJi+acuumTtuihjO+8muWtmOasvizmiYvmnLrpk7booYzv\r\nvJrnkIbotKI=", "5omL5py66ZO26KGM77ya5pyI5bqm6LSm5Y2V", "5omL5py66ZO26KGM77ya5a2Y5qy+LOaJi+acuumTtuihjO+8mueQhui0og==", "5omL5py66ZO26KGM77ya55CG6LSiLOaJi+acuumTtuihjO+8muWtmOasvg==", "5Y+R6YCB56uZ5YaF5raI5oGv", "5omL5py66ZO26KGM77ya5L+h55So5Y2hLOaJi+acuumTtuihjO+8muWtmOasvizmiYvmnLrpk7bo\r\noYzvvJrnkIbotKI=", "5omL5py66ZO26KGM77ya5L+h55So5Y2hLOaJi+acuumTtuihjO+8muWtmOasvg==", "5omL5py66ZO26KGM77ya6L2s6LSm5rGH5qy+", "5omL5py66ZO26KGM77ya5pyI5bqm6LSm5Y2VLOaJi+acuumTtuihjO+8mueQhui0og=="], "title": "用户操作路径分布", "componentId": 105439, "pqlList": [], "prompt": "\"\"\"\n       首先你需要用精炼的语言重写以下的统计结论，然后你再提炼出该流程路径的特点（10个字以内），最后你要用50个字左右给出对该流程路径的优化建议。\n       你的回答要遵守输出格式，回答过程请参考提供的流程节点和流程图的信息。\n       流程图:{\"variantList\":[{\"varName\":\"#1\",\"value\":9.34,\"count\":63569,\"variant\":\"手机银行：存款\",\"pathKpiValue\":\"157.07911088738223\",\"variantMd5\":\"5omL5py66ZO26KGM77ya5a2Y5qy+\",\"eventList\":\"手机银行：存款\",\"eventSize\":1,\"caseDuration\":157.07911088738223,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:9.34%\"},{\"value\":\"案例数量:63569\"}]},{\"varName\":\"#2\",\"value\":3.38,\"count\":23026,\"variant\":\"手机银行：理财,手机银行：存款,手机银行：理财\",\"pathKpiValue\":\"281.24463649787197\",\"variantMd5\":\"5omL5py66ZO26KGM77ya55CG6LSiLOaJi+acuumTtuihjO+8muWtmOasvizmiYvmnLrpk7booYzv\\r\\nvJrnkIbotKI=\",\"eventList\":\"手机银行：理财,手机银行：存款,手机银行：理财\",\"eventSize\":3,\"caseDuration\":281.24463649787197,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:3.38%\"},{\"value\":\"案例数量:23026\"}]},{\"varName\":\"#3\",\"value\":3.22,\"count\":21898,\"variant\":\"手机银行：月度账单\",\"pathKpiValue\":\"98.91012877888392\",\"variantMd5\":\"5omL5py66ZO26KGM77ya5pyI5bqm6LSm5Y2V\",\"eventList\":\"手机银行：月度账单\",\"eventSize\":1,\"caseDuration\":98.91012877888392,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:3.22%\"},{\"value\":\"案例数量:21898\"}]},{\"varName\":\"#4\",\"value\":3.08,\"count\":20951,\"variant\":\"手机银行：存款,手机银行：理财\",\"pathKpiValue\":\"361.64607894611237\",\"variantMd5\":\"5omL5py66ZO26KGM77ya5a2Y5qy+LOaJi+acuumTtuihjO+8mueQhui0og==\",\"eventList\":\"手机银行：存款,手机银行：理财\",\"eventSize\":2,\"caseDuration\":361.64607894611237,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:3.08%\"},{\"value\":\"案例数量:20951\"}]},{\"varName\":\"#5\",\"value\":3.05,\"count\":20803,\"variant\":\"手机银行：理财,手机银行：存款\",\"pathKpiValue\":\"219.54040282651542\",\"variantMd5\":\"5omL5py66ZO26KGM77ya55CG6LSiLOaJi+acuumTtuihjO+8muWtmOasvg==\",\"eventList\":\"手机银行：理财,手机银行：存款\",\"eventSize\":2,\"caseDuration\":219.54040282651542,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:3.05%\"},{\"value\":\"案例数量:20803\"}]},{\"varName\":\"#6\",\"value\":2.8,\"count\":19105,\"variant\":\"发送站内消息\",\"pathKpiValue\":\"3206427.292384193\",\"variantMd5\":\"5Y+R6YCB56uZ5YaF5raI5oGv\",\"eventList\":\"发送站内消息\",\"eventSize\":1,\"caseDuration\":3206427.292384193,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:2.8%\"},{\"value\":\"案例数量:19105\"}]},{\"varName\":\"#7\",\"value\":2.78,\"count\":18925,\"variant\":\"手机银行：信用卡,手机银行：存款,手机银行：理财\",\"pathKpiValue\":\"265.5468956406869\",\"variantMd5\":\"5omL5py66ZO26KGM77ya5L+h55So5Y2hLOaJi+acuumTtuihjO+8muWtmOasvizmiYvmnLrpk7bo\\r\\noYzvvJrnkIbotKI=\",\"eventList\":\"手机银行：信用卡,手机银行：存款,手机银行：理财\",\"eventSize\":3,\"caseDuration\":265.5468956406869,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:2.78%\"},{\"value\":\"案例数量:18925\"}]},{\"varName\":\"#8\",\"value\":2.31,\"count\":15715,\"variant\":\"手机银行：信用卡,手机银行：存款\",\"pathKpiValue\":\"218.8390709513204\",\"variantMd5\":\"5omL5py66ZO26KGM77ya5L+h55So5Y2hLOaJi+acuumTtuihjO+8muWtmOasvg==\",\"eventList\":\"手机银行：信用卡,手机银行：存款\",\"eventSize\":2,\"caseDuration\":218.8390709513204,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:2.31%\"},{\"value\":\"案例数量:15715\"}]},{\"varName\":\"#9\",\"value\":1.57,\"count\":10736,\"variant\":\"手机银行：转账汇款\",\"pathKpiValue\":\"276.7943368107303\",\"variantMd5\":\"5omL5py66ZO26KGM77ya6L2s6LSm5rGH5qy+\",\"eventList\":\"手机银行：转账汇款\",\"eventSize\":1,\"caseDuration\":276.7943368107303,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:1.57%\"},{\"value\":\"案例数量:10736\"}]},{\"varName\":\"#10\",\"value\":1.13,\"count\":7699,\"variant\":\"手机银行：月度账单,手机银行：理财\",\"pathKpiValue\":\"286.5569554487596\",\"variantMd5\":\"5omL5py66ZO26KGM77ya5pyI5bqm6LSm5Y2VLOaJi+acuumTtuihjO+8mueQhui0og==\",\"eventList\":\"手机银行：月度账单,手机银行：理财\",\"eventSize\":2,\"caseDuration\":286.5569554487596,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:1.13%\"},{\"value\":\"案例数量:7699\"}]},{\"varName\":\"其他\",\"value\":67.29,\"count\":457579,\"variant\":\"其他\",\"pathKpiValue\":\"735.8865572939318\",\"variantMd5\":\"其他\",\"eventList\":\"其他\",\"eventSize\":1,\"caseDuration\":735.8865572939318,\"skip\":null,\"valueList\":[{\"value\":\"所占比例:67.29%\"},{\"value\":\"案例数量:457579\"}]}],\"variant统计\":{\"variant总数\":114607,\"case总数\":680006}}\\\\n\n       流程节点:{\"eventNodes\":[{\"name\":\"手机银行：月度账单\",\"number\":29597},{\"name\":\"手机银行：信用卡\",\"number\":34640},{\"name\":\"手机银行：理财\",\"number\":91404},{\"name\":\"*开始*\",\"number\":222427},{\"name\":\"手机银行：存款\",\"number\":162989},{\"name\":\"发送站内消息\",\"number\":19105},{\"name\":\"*结束*\",\"number\":222427},{\"name\":\"手机银行：转账汇款\",\"number\":10736}],\"lineNodes\":[{\"source\":\"手机银行：月度账单\",\"target\":\"手机银行：理财\",\"number\":null,\"duration\":\"276.9秒\"},{\"source\":\"手机银行：存款\",\"target\":\"手机银行：理财\",\"number\":null,\"duration\":\"273.4秒\"},{\"source\":\"手机银行：信用卡\",\"target\":\"手机银行：存款\",\"number\":null,\"duration\":\"33.4秒\"},{\"source\":\"手机银行：理财\",\"target\":\"手机银行：存款\",\"number\":null,\"duration\":\"33.0秒\"},{\"source\":\"*开始*\",\"target\":\"发送站内消息\",\"number\":null,\"duration\":\"\"},{\"source\":\"发送站内消息\",\"target\":\"*结束*\",\"number\":null,\"duration\":\"\"},{\"source\":\"*开始*\",\"target\":\"手机银行：存款\",\"number\":null,\"duration\":\"\"},{\"source\":\"手机银行：存款\",\"target\":\"*结束*\",\"number\":null,\"duration\":\"\"},{\"source\":\"*开始*\",\"target\":\"手机银行：信用卡\",\"number\":null,\"duration\":\"\"},{\"source\":\"手机银行：理财\",\"target\":\"*结束*\",\"number\":null,\"duration\":\"\"},{\"source\":\"*开始*\",\"target\":\"手机银行：月度账单\",\"number\":null,\"duration\":\"\"},{\"source\":\"手机银行：月度账单\",\"target\":\"*结束*\",\"number\":null,\"duration\":\"\"},{\"source\":\"*开始*\",\"target\":\"手机银行：理财\",\"number\":null,\"duration\":\"\"},{\"source\":\"*开始*\",\"target\":\"手机银行：转账汇款\",\"number\":null,\"duration\":\"\"},{\"source\":\"手机银行：转账汇款\",\"target\":\"*结束*\",\"number\":null,\"duration\":\"\"}]}\\\\n\n       统计结论:流程中总共有680006个案例,分布在114607个不同的流程路径中,前十大路径中的案例占总案例的32.660000000000004%。其中耗时最长的路径经历了\"37.1114天}\",所占比例为2.8%。耗时最短的路径经历了\"0.0011天}\",所占比例为3.22%。当前选中的10个路径所包含的案例共222427个,占案例总数的32.660000000000004%,平均耗时为\"3.1899天}\"。\\\\n\n       输出格式:'统计结论:\\\\n流程特点:\\\\n改进建议:\\\\n'\n\n'''\n      \"\"\""}, {"metadata": [{"columnName": "产品名称", "originColumn": "产品名称", "columnType": 4, "expressionLhs": "`prodEventlog`.`eventName`"}, {"columnName": "活跃用户数", "originColumn": "活跃用户数", "columnType": 1, "expressionLhs": "count(distinct `prodEventlog`.`caseID`)"}, {"columnName": "使用总次数", "originColumn": "使用总次数", "columnType": 1, "expressionLhs": "count(`prodEventlog`.`caseID`)"}, {"columnName": "平均单次使用时间", "originColumn": "平均单次使用时间", "columnType": 2, "expressionLhs": "KPI('avgProdStay')"}], "data": [["手机银行：存款", "17425", "360900", "55.9"], ["手机银行：理财", "15218", "95448", "36.1"], ["手机银行：月度账单", "13659", "97880", "65.6"], ["手机银行：贷款", "10874", "109443", "61.0"], ["手机银行：信用卡", "9827", "44625", "40.4"], ["手机银行：转账汇款", "9132", "264037", "53.0"], ["手机银行：生活缴费", "5048", "10431", "56.1"], ["手机银行：结售汇", "4420", "11104", "74.5"], ["手机银行：权益中心", "2526", "3598", "62.9"], ["手机银行：便民服务", "2468", "4016", "78.9"]], "unit": ["", "", "", "秒"], "title": "活约用户数量最多的模块（Top 10）", "componentId": 105404, "pqlList": [{"name": "产品名称", "expression": "`prodEventlog`.`eventName`", "unit": ""}, {"name": "活跃用户数", "expression": "count(distinct `prodEventlog`.`caseID`)", "unit": ""}, {"name": "使用总次数", "expression": "count(`prodEventlog`.`caseID`)", "unit": ""}, {"name": "平均单次使用时间", "expression": "KPI('avgProdStay')", "unit": "秒"}], "prompt": "请你根据我提供的柱状图数据，总结出该柱状图中数据的特点，字数不要超过100字。然后分析出该柱状图中数据的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定。文字尽量简洁精炼。\n'''\n数据:{\"data\":{\"metadata\":[{\"columnName\":\"产品名称\",\"originColumn\":\"产品名称\"},{\"columnName\":\"活跃用户数\",\"originColumn\":\"活跃用户数\"},{\"columnName\":\"使用总次数\",\"originColumn\":\"使用总次数\"},{\"columnName\":\"平均单次使用时间\",\"originColumn\":\"平均单次使用时间\"}],\"data\":[[\"手机银行：存款\",\"17425\",\"360900\",\"55.9\"],[\"手机银行：理财\",\"15218\",\"95448\",\"36.1\"],[\"手机银行：月度账单\",\"13659\",\"97880\",\"65.6\"],[\"手机银行：贷款\",\"10874\",\"109443\",\"61.0\"],[\"手机银行：信用卡\",\"9827\",\"44625\",\"40.4\"],[\"手机银行：转账汇款\",\"9132\",\"264037\",\"53.0\"],[\"手机银行：生活缴费\",\"5048\",\"10431\",\"56.1\"],[\"手机银行：结售汇\",\"4420\",\"11104\",\"74.5\"],[\"手机银行：权益中心\",\"2526\",\"3598\",\"62.9\"],[\"手机银行：便民服务\",\"2468\",\"4016\",\"78.9\"]],\"unit\":[\"\",\"\",\"\",\"秒\"]}}\n'''\n\n\n"}, {"kpiValue": "26.6k", "title": "客户总数", "componentId": 105403, "simple": true, "pqlList": [], "prompt": null}, {"kpiValue": "1.76k", "title": "活跃用户（7日平均）", "componentId": 105407, "simple": true, "pqlList": [], "prompt": null}, {"kpiValue": "10.3", "title": "新增用户（7日平均）", "componentId": 105410, "simple": true, "pqlList": [], "prompt": null}, {"kpiValue": "289", "title": "活跃用户（当日）", "componentId": 105413, "simple": true, "pqlList": [], "prompt": null}, {"kpiValue": "13.0", "title": "新增用户（当日）", "componentId": 105415, "simple": true, "pqlList": [], "prompt": null}, {"kpiValue": "43.20%", "title": "用户次日留存（7日平均）", "componentId": 105416, "simple": true, "pqlList": [], "prompt": null}, {"metadata": [{"columnName": "页面名称", "originColumn": "页面名称", "columnType": 4, "expressionLhs": "concat(`prodEventlog`.`eventName`, '-详情页')"}, {"columnName": "活跃用户数", "originColumn": "活跃用户数", "columnType": 1, "expressionLhs": "count(distinct `prodEventlog`.`caseID`)"}, {"columnName": "使用总次数", "originColumn": "使用总次数", "columnType": 1, "expressionLhs": "count(`prodEventlog`.`caseID`)"}, {"columnName": "平均单次使用时间", "originColumn": "平均单次使用时间", "columnType": 2, "expressionLhs": "KPI('avgProdStay')"}], "data": [["手机银行：存款-详情页", "17425", "360900", "55.9"], ["手机银行：理财-详情页", "15218", "95448", "36.1"], ["手机银行：月度账单-详情页", "13659", "97880", "65.6"], ["手机银行：贷款-详情页", "10874", "109443", "61.0"], ["手机银行：信用卡-详情页", "9827", "44625", "40.4"], ["手机银行：转账汇款-详情页", "9132", "264037", "53.0"], ["手机银行：生活缴费-详情页", "5048", "10431", "56.1"], ["手机银行：结售汇-详情页", "4420", "11104", "74.5"], ["手机银行：权益中心-详情页", "2526", "3598", "62.9"], ["手机银行：便民服务-详情页", "2468", "4016", "78.9"]], "unit": ["", "", "", "秒"], "title": "活约用户数量最多的页面（Top 10）", "componentId": 105419, "pqlList": [{"name": "页面名称", "expression": "concat(`prodEventlog`.`eventName`, '-详情页')", "unit": ""}, {"name": "活跃用户数", "expression": "count(distinct `prodEventlog`.`caseID`)", "unit": ""}, {"name": "使用总次数", "expression": "count(`prodEventlog`.`caseID`)", "unit": ""}, {"name": "平均单次使用时间", "expression": "KPI('avgProdStay')", "unit": "秒"}], "prompt": "请你根据我提供的柱状图数据，总结出该柱状图中数据的特点，字数不要超过100字。然后分析出该柱状图中数据的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定。文字尽量简洁精炼。\n'''\n数据:{\"data\":{\"metadata\":[{\"columnName\":\"页面名称\",\"originColumn\":\"页面名称\"},{\"columnName\":\"活跃用户数\",\"originColumn\":\"活跃用户数\"},{\"columnName\":\"使用总次数\",\"originColumn\":\"使用总次数\"},{\"columnName\":\"平均单次使用时间\",\"originColumn\":\"平均单次使用时间\"}],\"data\":[[\"手机银行：存款-详情页\",\"17425\",\"360900\",\"55.9\"],[\"手机银行：理财-详情页\",\"15218\",\"95448\",\"36.1\"],[\"手机银行：月度账单-详情页\",\"13659\",\"97880\",\"65.6\"],[\"手机银行：贷款-详情页\",\"10874\",\"109443\",\"61.0\"],[\"手机银行：信用卡-详情页\",\"9827\",\"44625\",\"40.4\"],[\"手机银行：转账汇款-详情页\",\"9132\",\"264037\",\"53.0\"],[\"手机银行：生活缴费-详情页\",\"5048\",\"10431\",\"56.1\"],[\"手机银行：结售汇-详情页\",\"4420\",\"11104\",\"74.5\"],[\"手机银行：权益中心-详情页\",\"2526\",\"3598\",\"62.9\"],[\"手机银行：便民服务-详情页\",\"2468\",\"4016\",\"78.9\"]],\"unit\":[\"\",\"\",\"\",\"秒\"]}}\n'''\n\n\n"}, {"kpiValue": "60", "title": "使用时长(当日)", "componentId": 105414, "simple": true, "pqlList": [], "prompt": null}, {"kpiValue": "53", "title": "使用时长(7日平均)", "componentId": 105408, "simple": true, "pqlList": [], "prompt": null}, {"metadata": [{"columnName": "产品名称", "originColumn": "产品名称", "columnType": 4, "expressionLhs": "`prodEventlog`.`eventName`"}, {"columnName": "活跃用户数", "originColumn": "活跃用户数", "columnType": 1, "expressionLhs": "count(distinct `prodEventlog`.`caseID`)"}, {"columnName": "使用总次数", "originColumn": "使用总次数", "columnType": 1, "expressionLhs": "count(`prodEventlog`.`caseID`)"}, {"columnName": "平均单次使用时间", "originColumn": "平均单次使用时间", "columnType": 2, "expressionLhs": "KPI('avgProdStay')"}], "data": [["手机银行：投诉咨询-我的服务", "60", "76", "113.0"], ["手机银行：专属理财", "83", "139", "31.2"], ["手机银行：个人养老金", "84", "555", "20.6"], ["手机银行：我的应用", "89", "108", "79.8"], ["手机银行：远程办", "112", "141", "178.2"], ["手机银行：投诉咨询-服务总览", "124", "146", "111.4"], ["手机银行：投诉咨询-服务订阅", "144", "206", "46.9"], ["手机银行：投诉咨询", "147", "207", "70.0"], ["手机银行：投诉咨询-服务助手", "165", "1852", "40.3"], ["手机银行：个人资料", "191", "413", "53.9"]], "unit": ["", "", "", "秒"], "title": "活约用户数量最少的模块（Tail 10）", "componentId": 105418, "pqlList": [{"name": "产品名称", "expression": "`prodEventlog`.`eventName`", "unit": ""}, {"name": "活跃用户数", "expression": "count(distinct `prodEventlog`.`caseID`)", "unit": ""}, {"name": "使用总次数", "expression": "count(`prodEventlog`.`caseID`)", "unit": ""}, {"name": "平均单次使用时间", "expression": "KPI('avgProdStay')", "unit": "秒"}], "prompt": "请你根据我提供的柱状图数据，总结出该柱状图中数据的特点，字数不要超过100字。然后分析出该柱状图中数据的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定。文字尽量简洁精炼。\n'''\n数据:{\"data\":{\"metadata\":[{\"columnName\":\"产品名称\",\"originColumn\":\"产品名称\"},{\"columnName\":\"活跃用户数\",\"originColumn\":\"活跃用户数\"},{\"columnName\":\"使用总次数\",\"originColumn\":\"使用总次数\"},{\"columnName\":\"平均单次使用时间\",\"originColumn\":\"平均单次使用时间\"}],\"data\":[[\"手机银行：投诉咨询-我的服务\",\"60\",\"76\",\"113.0\"],[\"手机银行：专属理财\",\"83\",\"139\",\"31.2\"],[\"手机银行：个人养老金\",\"84\",\"555\",\"20.6\"],[\"手机银行：我的应用\",\"89\",\"108\",\"79.8\"],[\"手机银行：远程办\",\"112\",\"141\",\"178.2\"],[\"手机银行：投诉咨询-服务总览\",\"124\",\"146\",\"111.4\"],[\"手机银行：投诉咨询-服务订阅\",\"144\",\"206\",\"46.9\"],[\"手机银行：投诉咨询\",\"147\",\"207\",\"70.0\"],[\"手机银行：投诉咨询-服务助手\",\"165\",\"1852\",\"40.3\"],[\"手机银行：个人资料\",\"191\",\"413\",\"53.9\"]],\"unit\":[\"\",\"\",\"\",\"秒\"]}}\n'''\n\n\n"}, {"metadata": [{"columnName": "页面名称", "originColumn": "页面名称", "columnType": 4, "expressionLhs": "concat(`prodEventlog`.`eventName`, '-详情页')"}, {"columnName": "活跃用户数", "originColumn": "活跃用户数", "columnType": 1, "expressionLhs": "count(distinct `prodEventlog`.`caseID`)"}, {"columnName": "使用总次数", "originColumn": "使用总次数", "columnType": 1, "expressionLhs": "count(`prodEventlog`.`caseID`)"}, {"columnName": "平均单次使用时间", "originColumn": "平均单次使用时间", "columnType": 2, "expressionLhs": "KPI('avgProdStay')"}], "data": [["手机银行：投诉咨询-我的服务-详情页", "60", "76", "113.0"], ["手机银行：专属理财-详情页", "83", "139", "31.2"], ["手机银行：个人养老金-详情页", "84", "555", "20.6"], ["手机银行：我的应用-详情页", "89", "108", "79.8"], ["手机银行：远程办-详情页", "112", "141", "178.2"], ["手机银行：投诉咨询-服务总览-详情页", "124", "146", "111.4"], ["手机银行：投诉咨询-服务订阅-详情页", "144", "206", "46.9"], ["手机银行：投诉咨询-详情页", "147", "207", "70.0"], ["手机银行：投诉咨询-服务助手-详情页", "165", "1852", "40.3"], ["手机银行：个人资料-详情页", "191", "413", "53.9"]], "unit": ["", "", "", "秒"], "title": "活约用户数量最少的页面（Tail 10）", "componentId": 105420, "pqlList": [{"name": "页面名称", "expression": "concat(`prodEventlog`.`eventName`, '-详情页')", "unit": ""}, {"name": "活跃用户数", "expression": "count(distinct `prodEventlog`.`caseID`)", "unit": ""}, {"name": "使用总次数", "expression": "count(`prodEventlog`.`caseID`)", "unit": ""}, {"name": "平均单次使用时间", "expression": "KPI('avgProdStay')", "unit": "秒"}], "prompt": "请你根据我提供的柱状图数据，总结出该柱状图中数据的特点，字数不要超过100字。然后分析出该柱状图中数据的问题点，并分别给出相应的改进意见。问题点的数量可以由你自己决定。文字尽量简洁精炼。\n'''\n数据:{\"data\":{\"metadata\":[{\"columnName\":\"页面名称\",\"originColumn\":\"页面名称\"},{\"columnName\":\"活跃用户数\",\"originColumn\":\"活跃用户数\"},{\"columnName\":\"使用总次数\",\"originColumn\":\"使用总次数\"},{\"columnName\":\"平均单次使用时间\",\"originColumn\":\"平均单次使用时间\"}],\"data\":[[\"手机银行：投诉咨询-我的服务-详情页\",\"60\",\"76\",\"113.0\"],[\"手机银行：专属理财-详情页\",\"83\",\"139\",\"31.2\"],[\"手机银行：个人养老金-详情页\",\"84\",\"555\",\"20.6\"],[\"手机银行：我的应用-详情页\",\"89\",\"108\",\"79.8\"],[\"手机银行：远程办-详情页\",\"112\",\"141\",\"178.2\"],[\"手机银行：投诉咨询-服务总览-详情页\",\"124\",\"146\",\"111.4\"],[\"手机银行：投诉咨询-服务订阅-详情页\",\"144\",\"206\",\"46.9\"],[\"手机银行：投诉咨询-详情页\",\"147\",\"207\",\"70.0\"],[\"手机银行：投诉咨询-服务助手-详情页\",\"165\",\"1852\",\"40.3\"],[\"手机银行：个人资料-详情页\",\"191\",\"413\",\"53.9\"]],\"unit\":[\"\",\"\",\"\",\"秒\"]}}\n'''\n\n\n"}]