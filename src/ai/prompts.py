from typing import Dict, Any, List, Union, Optional

class PromptTemplates:
    """提示词模板集合"""
    
    @staticmethod
    def polish_title(title: str, domain: Optional[str] = None) -> str:
        """生成标题润色的提示"""
        domain_context = ""
        if domain:
            domain_context = f"\n\n请注意，这个标题应该应用于{domain}领域的专业场景，确保使用该领域内的术语和表达方式。"
            
        return f"""请将以下标题润色成一句专业又富有深意的表达。要求在保持技术专业性的基础上，增加内涵和洞察力。

原标题: {title}

润色要求：
1. 保留和强化专业技术术语，体现专业性
2. 在专业性基础上增加深度洞察，让标题：
   - 暗示数据背后的本质问题
   - 体现分析的深层价值
   - 突出技术手段背后的商业智慧
   - 展现专业分析的独特视角
3. 可以运用的手法：
   - 将技术概念与深层含义结合
   - 用专业术语表达策略思维
   - 展现技术分析的前瞻性
   - 突出专业分析的核心价值
4. 保持专业严谨性，避免过于花哨
5. 控制在10-15个字之内
6. 让标题既显示专业能力，又体现深度思考{domain_context}

示例对比：
- 普通：销售数据分析报告 → 润色：销售漏斗转化效率深度剖析
- 普通：用户行为研究 → 润色：用户画像构建与行为预测模型
- 普通：财务状况分析 → 润色：财务健康指数与风险识别矩阵
- 普通：市场趋势预测 → 润色：市场动向预警与机会识别框架

润色后的标题:"""
    
    @staticmethod
    def generate_simple_title(modules: List[Dict[str, Any]], domain: Optional[str] = None) -> str:
        """为简单模块组生成标题的提示"""
        module_info = "\n".join([
            f"- {module.get('title', '未命名')}: {module.get('kpiValue', '无值')}" 
            for module in modules
        ])
        
        domain_context = ""
        if domain:
            domain_context = f"\n6. 确保标题反映{domain}领域的专业性，使用该领域内常见的术语"
        
        return f"""请为以下指标集合创建一个自然、简洁的标题。标题应该像一句话总结，告诉读者这部分讲什么。

指标集合:
{module_info}

要求：
1. 使用专业的分析术语，体现技术深度
2. 准确反映这些指标的分析价值和业务含义
3. 控制在12-18个字之间，确保信息完整
4. 体现数据背后的深层洞察和战略意义
5. 让标题具有专业性和前瞻性{domain_context}

请创建一个标题:"""
    
    @staticmethod
    def analyze_simple_modules(modules: List[Dict[str, Any]], domain: Optional[str] = None) -> str:
        """为简单模块组生成分析内容的提示"""
        module_info = "\n".join([
            f"- {module.get('title', '未命名')}: {module.get('kpiValue', '无值')}" 
            for module in modules
        ])
        
        domain_context = ""
        if domain:
            domain_context = f"""
领域背景：
请注意，你需要从{domain}领域的专业角度分析这些数据。请确保：
- 使用{domain}领域内的专业术语和表达方式
- 分析思路和框架符合{domain}领域的最佳实践
- 生成的洞察具有该领域的深度和专业性
- 挖掘指标在{domain}领域中的特殊意义和价值
"""
        
        return f"""请基于以下指标数据，进行深度数据挖掘和洞察分析。根据数据的实际特点和内在联系，自主确定最适合的分析角度和标题。

指标集合:
{module_info}
{domain_context}

分析要求：
1. 仔细观察这些数据的特点，根据数据性质确定分析角度
2. 自主生成3-5个有针对性的一级小标题，小标题应该：
   - 直接反映这组数据的核心特征和价值
   - 突出最值得关注的洞察点
   - 避免使用通用、模板化的标题
   - 根据实际数据内容创造独特的标题
   - 每个标题都要自然贴切，能准确概括该部分的分析重点

格式要求：
1. 结构层次：
   - 一级小标题：使用"数字、标题"格式，如"1、xxx"
   - 内容：直接在一级小标题下方展开，不再使用二级小标题
   - 每个一级小标题下的内容应当是一段完整、连贯的分析文字
   
2. 标题生成原则：
   - 根据实际分析内容创造性地命名
   - 避免固定模式，让标题自然流畅
   - 确保标题准确反映该部分的核心内容
   - 使用专业术语但不生硬

3. 格式示例：
   1、[根据内容生成的独特标题]
   这里直接开始一段完整的分析内容，融合了该标题下所有相关要点，形成一个连贯的整体观点。内容应当既有深度又有广度，涵盖该方面的所有关键信息和洞察。这段分析应当清晰、专业且富有洞察力，直接反映数据背后的核心价值和意义。

   2、[另一个根据内容生成的独特标题]
   这里同样是一段完整的分析文字，直接展开该标题下的所有内容。避免使用任何二级标题或标记符号，而是以自然流畅的方式呈现分析结果和洞察发现。

4. 避免使用任何格式标记符号（如*、#、-、·等）和任何形式的编号（如(1)、1.、①等）
5. 确保每个分析点都基于数据事实，提供具体而深入的洞察
6. 标题不要带有[]

让每个标题都能准确反映该部分内容的独特价值，避免千篇一律的表述方式。"""
    
    @staticmethod
    def analyze_module(title: str, prompt_content: str, module_data: str = "", domain: Optional[str] = None) -> str:
        """为常规模块生成分析内容的提示"""
        data_section = ""
        if module_data:
            data_section = f"""
模块数据概览:
{module_data}
"""
        
        domain_context = ""
        if domain:
            domain_context = f"""
领域背景：
请注意，你需要从{domain}领域的专业角度分析这些数据。请确保：
- 使用{domain}领域内的专业术语和表达方式
- 分析思路和框架符合{domain}领域的最佳实践
- 生成的洞察具有该领域的深度和专业性
- 结合{domain}领域的特点提供有价值的解读和建议
"""
            
        return f"""请对以下数据进行深度分析和挖掘。根据标题内容和数据特征，自主确定最合适的分析框架，创造性地生成各级标题。

模块标题: {title}
{data_section}
任务说明: {prompt_content}
{domain_context}

分析要求：
1. 深入理解模块标题和数据特点，创造性地构建分析框架
2. 自主生成4-6个有针对性的一级小标题
3. 标题生成原则：
   - 根据实际内容创造独特、贴切的标题
   - 避免使用模板化的标题（如"统计结论"、"流程特点"等）
   - 让标题自然地反映该部分的核心洞察
   - 使用专业术语，但表述要自然流畅
   - 每个标题都应该是对内容的精准概括

格式要求：
1. 结构层次说明：
   - 一级小标题：使用"数字、标题"格式，如"1、xxx"
   - 内容：直接在一级小标题下方展开完整分析内容
   - 不使用任何二级小标题或标记符号
   
2. 重要提示：
   - 每个一级小标题后直接跟随一段完整、连贯的分析文字
   - 不要使用任何二级标题，即使是"总结"、"小结"等
   - 所有分析内容应当整合为一个自然流畅的段落
   - 避免使用任何标记符号或编号来分割内容
   
3. 格式示例：
   1、[基于内容创造的独特一级标题]
   直接开始一段完整的分析，融合了该标题下所有相关要点，形成一个连贯的整体观点。分析应当既有深度又有广度，涵盖该方面的所有关键信息和洞察。这段分析应当清晰、专业且富有洞察力，直接反映数据背后的核心价值和意义。分析中可以包含核心发现、主要问题和可能的优化方向，但都应当在同一个连贯段落中自然呈现。
   
   2、[另一个独创的一级标题]
   直接展开分析文字，不使用任何二级标题或标记词如"内容："等，而是以自然流畅的方式呈现分析结果和洞察发现。

4. 注意事项：
   - 每个标题都要独特且贴合内容
   - 避免使用通用模板，让分析结构自然生长
   - 确保标题既专业又易懂
   - 保持清晰的层次结构
   - 标题不要带有[]
   - 分析文字直接跟在标题后面，不要添加"内容："等标记词
   - 分析内容要形成完整的段落，不要使用任何标记或编号

让分析框架和标题自然地从数据洞察中生长出来，形成独特而有价值的分析报告。"""
    
    @staticmethod
    def summarize_report(modules_info: str, domain: Optional[str] = None) -> str:
        """生成报告总结的提示"""
        domain_context = ""
        if domain:
            domain_context = f"""
领域背景：
请以{domain}领域专家的视角总结这份报告。请确保：
- 使用{domain}领域内的专业术语和表达方式
- 摘要内容反映{domain}领域的关键关注点和价值观
- 总结的见解符合该领域的最佳实践和标准
- 突出报告对{domain}领域的特殊贡献和价值
"""
        
        return f"""请基于以下各模块的数据分析结果，生成一份综合性的洞察摘要。根据所有模块的内容特点，创造性地构建总结框架。

报告包含的模块:
{modules_info}
{domain_context}

总结要求：
1. 全面理解各模块的核心主题和关键发现
2. 根据整个报告的特色，自主生成3-4个独特的一级小标题
3. 每个小标题的创作原则：
   - 准确概括报告某个重要维度的内容
   - 体现跨模块的整合性思维
   - 避免使用套路化的总结语言
   - 突出报告的独特价值和核心洞察
   - 让标题自然流畅，富有洞察力

格式要求：
1. 结构层次：
   - 一级小标题：使用"数字、标题"格式，如"1、xxx"
   - 内容：直接在一级小标题下方展开完整分析内容
   - 不使用任何二级小标题或标记符号
   
2. 标题创作原则：
   - 从整体视角提炼核心价值
   - 让每个标题都能体现独特洞察
   - 避免千篇一律的总结模式
   - 确保标题既概括全面又不失精准
   
3. 格式示例：
   1、[基于报告核心价值的独特标题]
   直接开始一段完整的分析，融合了该标题下所有相关要点，形成一个连贯的整体观点。分析应当既有深度又有广度，涵盖该方面的所有关键信息和洞察。不使用任何二级标题或标记词如"内容："等，而是以自然流畅的方式呈现分析结果和洞察发现。

   2、[另一个独创的一级标题]
   直接展开完整的分析文字，整合所有相关要点，形成一个连贯的整体观点。

4. 避免使用任何格式标记符号（如*、#、-、·等）和任何形式的编号（如(1)、1.、①等）
5. 确保摘要简洁而深刻，控制在500字以内
6. 标题不要带有[]

让总结成为整个报告价值的升华，通过独特的标题和结构展现报告的核心贡献。"""