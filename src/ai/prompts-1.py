from typing import Dict, Any, List

class PromptTemplates:
    """提示词模板集合"""
    
    @staticmethod
    def polish_title(title: str) -> str:
        """生成标题润色的提示"""
        return f"""请将以下标题润色成一句专业又富有深意的表达。要求在保持技术专业性的基础上，增加内涵和洞察力。

原标题: {title}

润色要求：
1. 保留和强化专业技术术语，体现专业性
2. 在专业性基础上增加深度洞察，让标题：
   - 暗示数据背后的本质问题
   - 体现分析的深层价值
   - 突出技术手段背后的商业智慧
   - 展现专业分析的独特视角
3. 可以运用的手法：
   - 将技术概念与深层含义结合
   - 用专业术语表达策略思维
   - 展现技术分析的前瞻性
   - 突出专业分析的核心价值
4. 保持专业严谨性，避免过于花哨
5. 控制在10-15个字之内
6. 让标题既显示专业能力，又体现深度思考

示例对比：
- 普通：销售数据分析报告 → 润色：销售漏斗转化效率深度剖析
- 普通：用户行为研究 → 润色：用户画像构建与行为预测模型
- 普通：财务状况分析 → 润色：财务健康指数与风险识别矩阵
- 普通：市场趋势预测 → 润色：市场动向预警与机会识别框架

润色后的标题:"""
    
    @staticmethod
    def generate_simple_title(modules: List[Dict[str, Any]]) -> str:
        """为简单模块组生成标题的提示"""
        module_info = "\n".join([
            f"- {module.get('title', '未命名')}: {module.get('kpiValue', '无值')}" 
            for module in modules
        ])
        
        return f"""请为以下指标集合创建一个自然、简洁的标题。标题应该像一句话总结，告诉读者这部分讲什么。

指标集合:
{module_info}

要求：
1. 使用专业的分析术语，体现技术深度
2. 准确反映这些指标的分析价值和业务含义
3. 控制在12-18个字之间，确保信息完整
4. 体现数据背后的深层洞察和战略意义
5. 让标题具有专业性和前瞻性

请创建一个标题:"""
    
    @staticmethod
    def analyze_simple_modules(modules: List[Dict[str, Any]]) -> str:
        """为简单模块组生成分析内容的提示"""
        module_info = "\n".join([
            f"- {module.get('title', '未命名')}: {module.get('kpiValue', '无值')}" 
            for module in modules
        ])
        
        return f"""请基于以下指标数据，进行深度数据挖掘和洞察分析。根据数据的实际特点和内在联系，自主确定最适合的分析角度和标题。

指标集合:
{module_info}

分析要求：
1. 仔细观察这些数据的特点，根据数据性质确定分析角度
2. 自主生成3-5个有针对性的一级小标题，小标题应该：
   - 直接反映这组数据的核心特征和价值
   - 突出最值得关注的洞察点
   - 避免使用通用、模板化的标题
   - 根据实际数据内容创造独特的标题
   - 每个标题都要自然贴切，能准确概括该部分的分析重点

格式要求：
1. 结构层次：
   - 一级小标题：使用"数字、标题"格式，标题要根据具体分析内容自然生成
   - 二级小标题：根据分析内容自然命名，如"数据分布特征："、"关键价值点："等，要贴合实际内容
   - 内容点：在二级小标题下用一句话总结细致的内容
   
2. 标题生成原则：
   - 根据实际分析内容创造性地命名
   - 避免固定模式，让标题自然流畅
   - 确保标题准确反映该部分的核心内容
   - 使用专业术语但不生硬

3. 格式示例：
   1、[根据内容生成的独特标题]
   
   [贴合内容的二级标题]：
   · 一大句话总结具体分析内容
   
   [另一个贴合内容的二级标题]：
   · 一大句话总结具体分析内容

4. 避免使用任何格式标记符号（如*、#、-等）
5. 确保每个分析点都基于数据事实，提供具体而深入的洞察
6. 标题不要带有[]
让每个标题都能准确反映该部分内容的独特价值，避免千篇一律的表述方式。"""
    
    @staticmethod
    def analyze_module(title: str, prompt_content: str, module_data: str = "") -> str:
        """为常规模块生成分析内容的提示"""
        data_section = ""
        if module_data:
            data_section = f"""
模块数据概览:
{module_data}
"""
            
        return f"""请对以下数据进行深度分析和挖掘。根据标题内容和数据特征，自主确定最合适的分析框架，创造性地生成各级标题。

模块标题: {title}
{data_section}
任务说明: {prompt_content}

分析要求：
1. 深入理解模块标题和数据特点，创造性地构建分析框架
2. 自主生成4-6个有针对性的一级小标题
3. 在每个一级小标题下，根据具体内容生成合适的二级小标题
4. 标题生成原则：
   - 根据实际内容创造独特、贴切的标题
   - 避免使用模板化的标题（如"统计结论"、"流程特点"等）
   - 让标题自然地反映该部分的核心洞察
   - 使用专业术语，但表述要自然流畅
   - 每个标题都应该是对内容的精准概括

格式要求：
1. 结构层次说明：
   - 一级小标题：使用"数字、[标题名称]"格式，标题要根据分析内容独创
   - 二级小标题：直接写贴合内容的标题名称加冒号，要自然且准确
   - 内容点：在二级小标题下用一大句话总结
   
2. 重要提示：
   - 当需要总结、小结或归纳时，应该将其作为二级小标题，而不是编号内容点
   - 例如："总结："、"核心发现："、"关键价值："等应该是二级标题
   
3. 格式示例：
   1、[基于内容创造的独特一级标题]
   
   [自然贴切的二级标题]：
   · 一大句话总结具体分析内容
   
   
   总结：  # 注意：这是二级标题，不使用编号
   · 一大句话总结具体分析内容
   
   2、[第二个独创的一级标题]
   
   [相应的二级标题]：
   · 一大句话总结具体分析内容
   ...

4. 注意事项：
   - 每个标题都要独特且贴合内容
   - "总结"、"小结"、"建议"、"结论"等应该作为二级标题
   - 避免使用通用模板，让分析结构自然生长
   - 确保标题既专业又易懂
   - 保持清晰的层次结构
5. 标题不要带有[]  
让分析框架和标题自然地从数据洞察中生长出来，形成独特而有价值的分析报告。"""
    
    @staticmethod
    def summarize_report(modules_info: str) -> str:
        """生成报告总结的提示"""
        return f"""请基于以下各模块的数据分析结果，生成一份综合性的洞察摘要。根据所有模块的内容特点，创造性地构建总结框架。

报告包含的模块:
{modules_info}

总结要求：
1. 全面理解各模块的核心主题和关键发现
2. 根据整个报告的特色，自主生成3-4个独特的一级小标题
3. 每个小标题的创作原则：
   - 准确概括报告某个重要维度的内容
   - 体现跨模块的整合性思维
   - 避免使用套路化的总结语言
   - 突出报告的独特价值和核心洞察
   - 让标题自然流畅，富有洞察力

格式要求：
1. 结构层次：
   - 一级小标题：使用"数字、[创造性标题]"格式
   - 二级小标题：根据内容自然生成，要贴切且有洞察力
   - 内容点：在二级小标题下使用· 一大句话总结具体分析内容
   
2. 标题创作原则：
   - 从整体视角提炼核心价值
   - 让每个标题都能体现独特洞察
   - 避免千篇一律的总结模式
   - 确保标题既概括全面又不失精准
   
3. 格式示例：
   1、[基于报告核心价值的独特标题]
   
   [贴合内容的二级标题]：
   · 一大句话总结具体分析内容

   
   [另一个自然的二级标题]：
   · 一大句话总结具体分析内容

4. 避免使用任何格式标记符号（如*、#、-等）
5. 确保摘要简洁而深刻，控制在500字以内
6. 标题不要带有[]

让总结成为整个报告价值的升华，通过独特的标题和结构展现报告的核心贡献。"""