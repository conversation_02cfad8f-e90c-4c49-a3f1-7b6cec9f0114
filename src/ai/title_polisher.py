import logging
from typing import Optional

from src.ai.client import AIClient
from src.ai.prompts import PromptTemplates
from config.ai_config import DEFAULT_DOMAIN

logger = logging.getLogger(__name__)

class TitlePolisher:
    """标题润色器，使用AI模型优化标题"""
    
    def __init__(self, ai_client: Optional[AIClient] = None, domain: Optional[str] = DEFAULT_DOMAIN):
        """
        初始化标题润色器
        
        参数:
            ai_client: AI客户端实例，如果为None则创建新实例
            domain: 领域名称，用于在提示中提供领域上下文
        """
        self.ai_client = ai_client or AIClient()
        self.domain = domain
        logger.info(f"标题润色器初始化完成，使用领域: {domain if domain else '通用'}")
    
    def polish(self, title: str) -> str:
        """
        润色标题
        
        参数:
            title: 原始标题
            
        返回:
            润色后的标题，如果失败则返回原标题
        """
        if not title or not title.strip():
            logger.warning("收到空标题，无法润色")
            return "未命名模块"
        
        logger.info(f"润色标题: '{title}'")
        
        # 生成提示
        prompt = PromptTemplates.polish_title(title, self.domain)
        
        # 获取AI响应
        response = self.ai_client.generate_completion(
            prompt=prompt,
            max_tokens=100,
            temperature=0.7
        )
        
        # 检查响应
        if not response:
            logger.warning(f"标题润色失败，使用原始标题: '{title}'")
            return title
        
        polished_title = response.strip()
        logger.info(f"润色成功: '{title}' -> '{polished_title}'")
        
        return polished_title