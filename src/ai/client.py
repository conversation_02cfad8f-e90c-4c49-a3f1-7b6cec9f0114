from openai import OpenAI
import logging
import time
from typing import Dict, Any, Optional, List
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.ai_config import API_KEY, BASE_URL, MODEL_NAME, DEFAULT_MAX_TOKENS, DEFAULT_TEMPERATURE
from config.ai_config import MAX_RETRIES, RETRY_DELAY, REQUEST_INTERVAL

logger = logging.getLogger(__name__)

class AIClient:
    """AI服务客户端，处理与AI模型的交互"""
    
    def __init__(self, api_key: str = API_KEY, base_url: str = BASE_URL):
        """初始化AI客户端"""
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        self.last_request_time = 0
        logger.info("AI客户端初始化完成")
    
    def _rate_limit_wait(self):
        """实现简单的请求速率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < REQUEST_INTERVAL:
            sleep_time = REQUEST_INTERVAL - time_since_last
            logger.debug(f"请求速率限制: 等待{sleep_time:.2f}秒")
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def generate_completion(
        self, 
        prompt: str, 
        model: str = MODEL_NAME, 
        max_tokens: int = DEFAULT_MAX_TOKENS, 
        temperature: float = DEFAULT_TEMPERATURE,
        retry_count: int = 0
    ) -> Optional[str]:
        """
        生成文本补全
        
        参数:
            prompt: 输入提示
            model: 模型名称
            max_tokens: 最大tokens数量
            temperature: 温度参数
            retry_count: 当前重试计数
            
        返回:
            生成的文本或None（如果失败）
        """
        self._rate_limit_wait()
        
        try:
            logger.debug(f"向{model}发送请求，提示长度: {len(prompt)}")
            
            completion = self.client.chat.completions.create(
                model=model,
                messages=[{'role': 'user', 'content': prompt}],
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            response_text = completion.choices[0].message.content.strip()
            logger.debug(f"收到响应，长度: {len(response_text)}")
            
            return response_text
            
        except Exception as e:
            logger.error(f"生成补全时出错: {str(e)}")
            
            # 实现重试逻辑
            if retry_count < MAX_RETRIES:
                retry_count += 1
                wait_time = RETRY_DELAY * retry_count
                logger.warning(f"第{retry_count}次重试，等待{wait_time}秒...")
                time.sleep(wait_time)
                return self.generate_completion(prompt, model, max_tokens, temperature, retry_count)
            else:
                logger.error(f"达到最大重试次数({MAX_RETRIES})，放弃请求")
                return None
    
    def batch_generate(self, prompts: List[str], **kwargs) -> List[Optional[str]]:
        """批量生成多个提示的回答"""
        results = []
        
        for i, prompt in enumerate(prompts):
            logger.info(f"处理批量请求 {i+1}/{len(prompts)}")
            result = self.generate_completion(prompt, **kwargs)
            results.append(result)
        
        return results