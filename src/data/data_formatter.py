from typing import Dict, List, Any, Optional, Union
import json
import pandas as pd
import logging

logger = logging.getLogger(__name__)

class DataFormatter:
    """数据格式化工具，处理不同类型的数据并转换为适合分析的格式"""
    
    @staticmethod
    def format_table_data(module: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """
        将表格数据转换为pandas DataFrame
        
        参数:
            module: 包含data和metadata的模块
            
        返回:
            格式化的DataFrame或None（如果无法转换）
        """
        try:
            # 检查必要的字段
            if 'data' not in module or not isinstance(module['data'], list):
                logger.warning(f"模块缺少有效的data字段: {module.get('title', 'unnamed')}")
                return None
            
            if 'metadata' not in module or not isinstance(module['metadata'], list):
                logger.warning(f"模块缺少有效的metadata字段: {module.get('title', 'unnamed')}")
                return None
            
            # 提取列名
            column_names = []
            for meta in module['metadata']:
                if isinstance(meta, dict) and 'columnName' in meta:
                    column_names.append(meta['columnName'])
            
            # 如果没有列名，使用索引作为列名
            if not column_names:
                if module['data'] and module['data'][0]:
                    column_names = [f'col_{i}' for i in range(len(module['data'][0]))]
                else:
                    logger.warning(f"无法确定列名: {module.get('title', 'unnamed')}")
                    return None
            
            # 创建DataFrame
            df = pd.DataFrame(module['data'], columns=column_names)
            
            # 尝试将数值列转换为数值类型
            for col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='ignore')
            
            return df
            
        except Exception as e:
            logger.error(f"格式化表格数据时出错: {e}")
            return None
    
    @staticmethod
    def format_kpi_data(modules: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        将KPI模块转换为标题-值对
        
        参数:
            modules: KPI模块列表
            
        返回:
            {标题: KPI值} 字典
        """
        kpi_data = {}
        
        for module in modules:
            if 'title' in module and 'kpiValue' in module:
                kpi_data[module['title']] = module['kpiValue']
        
        return kpi_data
    
    @staticmethod
    def format_flow_data(module: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化流程数据
        
        参数:
            module: 包含流程数据的模块
            
        返回:
            格式化的流程数据
        """
        formatted = {
            'title': module.get('title', 'Unnamed Flow'),
            'nodes': [],
            'edges': []
        }
        
        # 提取节点
        if 'eventNodes' in module:
            for node in module['eventNodes']:
                if isinstance(node, dict) and 'name' in node:
                    formatted['nodes'].append({
                        'id': node['name'],
                        'name': node['name'],
                        'count': node.get('number', 0)
                    })
        
        # 提取边
        if 'lineNodes' in module:
            for edge in module['lineNodes']:
                if isinstance(edge, dict) and 'source' in edge and 'target' in edge:
                    formatted['edges'].append({
                        'source': edge['source'],
                        'target': edge['target'],
                        'duration': edge.get('second', '0秒')
                    })
        
        return formatted
    
    @staticmethod
    def get_data_summary(module: Dict[str, Any]) -> str:
        """
        生成数据摘要文本
        
        参数:
            module: 数据模块
            
        返回:
            描述性摘要文本
        """
        summary_parts = []
        
        # 添加标题
        if 'title' in module:
            summary_parts.append(f"模块: {module['title']}")
        
        # KPI值
        if 'kpiValue' in module:
            summary_parts.append(f"KPI值: {module['kpiValue']}")
        
        # 表格数据摘要
        if 'data' in module and isinstance(module['data'], list):
            rows = len(module['data'])
            summary_parts.append(f"包含{rows}行数据")
            
            # 添加列信息
            if 'metadata' in module and isinstance(module['metadata'], list):
                columns = [meta.get('columnName', 'unnamed') for meta in module['metadata'] if isinstance(meta, dict)]
                summary_parts.append(f"列: {', '.join(columns)}")
        
        # 流程数据摘要
        if 'eventNodes' in module and isinstance(module['eventNodes'], list):
            nodes = len(module['eventNodes'])
            summary_parts.append(f"包含{nodes}个流程节点")
            
        if 'lineNodes' in module and isinstance(module['lineNodes'], list):
            edges = len(module['lineNodes'])
            summary_parts.append(f"包含{edges}个流程连接")
        
        # 变体数据摘要
        if 'variantList' in module and isinstance(module['variantList'], list):
            variants = len(module['variantList'])
            summary_parts.append(f"包含{variants}个流程变体")
        
        # 图片数据摘要
        if 'image' in module and module['image']:
            summary_parts.append("包含可视化图表")
        
        return "\n".join(summary_parts)