from typing import Dict, List, Any, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class JsonValidator:
    """JSON数据验证器，验证JSON结构是否符合预期"""
    
    @staticmethod
    def validate_module(module: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        验证单个模块的结构
        
        返回:
            (验证结果, 错误消息)
        """
        # 检查必要字段
        if 'title' not in module:
            return False, "模块缺少必要的'title'字段"
        
        # 检查simple模块是否具有必要的kpiValue
        if module.get('simple') is True and 'kpiValue' not in module:
            return False, f"简单模块'{module['title']}'缺少必要的'kpiValue'字段"
        
        # 验证数据结构 (如果存在)
        if 'data' in module:
            if not isinstance(module['data'], list):
                return False, f"模块'{module['title']}'的'data'字段必须是数组类型"
        
        # 验证图片字段 (如果存在且不是简单模块)
        if not module.get('simple', False) and 'image' in module:
            if not isinstance(module['image'], str):
                return False, f"模块'{module['title']}'的'image'字段必须是字符串类型"
        
        # 验证元数据结构 (如果存在)
        if 'metadata' in module:
            if not isinstance(module['metadata'], list):
                return False, f"模块'{module['title']}'的'metadata'字段必须是数组类型"
            
            # 检查每个元数据项是否具有columnName
            for i, item in enumerate(module['metadata']):
                if not isinstance(item, dict) or 'columnName' not in item:
                    return False, f"模块'{module['title']}'的metadata[{i}]缺少必要的'columnName'字段"
        
        return True, None
    
    @staticmethod
    def validate_all_modules(modules: List[Dict[str, Any]]) -> Tuple[bool, List[str]]:
        """
        验证所有模块
        
        返回:
            (全部验证通过, 错误消息列表)
        """
        all_valid = True
        errors = []
        
        for i, module in enumerate(modules):
            valid, error = JsonValidator.validate_module(module)
            if not valid:
                all_valid = False
                errors.append(f"模块 #{i+1} 验证失败: {error}")
                logger.warning(f"模块 #{i+1} 验证失败: {error}")
            
        return all_valid, errors