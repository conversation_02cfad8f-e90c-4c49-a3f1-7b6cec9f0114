import json
import os
from typing import Dict, List, Any, Union
import logging

logger = logging.getLogger(__name__)

class JsonLoader:
    """JSON数据加载器，处理从文件或字符串加载JSON数据"""
    
    @staticmethod
    def from_file(file_path: str) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """从文件加载JSON数据"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"JSON文件不存在: {file_path}")
                
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"成功从{file_path}加载JSON数据")
                return data
        except json.JSONDecodeError as e:
            logger.error(f"JSON格式错误: {e}")
            raise ValueError(f"JSON格式错误: {e}")
        except Exception as e:
            logger.error(f"加载JSON文件时发生错误: {e}")
            raise Exception(f"加载JSON文件时发生错误: {e}")
    
    @staticmethod
    def from_string(json_string: str) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """从字符串加载JSON数据"""
        try:
            data = json.loads(json_string)
            logger.info("成功从字符串加载JSON数据")
            return data
        except json.JSONDecodeError as e:
            logger.error(f"JSON字符串格式错误: {e}")
            raise ValueError(f"JSON字符串格式错误: {e}")
        except Exception as e:
            logger.error(f"解析JSON字符串时发生错误: {e}")
            raise Exception(f"解析JSON字符串时发生错误: {e}")
    
    @staticmethod
    def normalize_data(data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """将数据标准化为列表格式"""
        if isinstance(data, dict):
            return [data]
        elif isinstance(data, list):
            return data
        else:
            logger.error(f"不支持的数据类型: {type(data)}")
            raise TypeError(f"不支持的数据类型: {type(data)}")