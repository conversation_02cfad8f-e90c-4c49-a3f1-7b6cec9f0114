from typing import Dict, List, Any, Tuple
import logging

logger = logging.getLogger(__name__)

class ModuleExtractor:
    """从JSON数据中提取和分类模块"""
    
    @staticmethod
    def extract_modules(data: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        从数据中提取和分类简单模块和常规模块
        
        参数:
            data: JSON数据列表
            
        返回:
            (regular_modules, simple_modules)元组
        """
        regular_modules = []
        simple_modules = []
        
        for item in data:
            # 检查是否为模块（具有'title'键）
            if 'title' in item:
                # 判断是否为简单模块
                if item.get('simple', False) is True:
                    simple_modules.append(item)
                    logger.debug(f"提取到简单模块: {item['title']}")
                else:
                    regular_modules.append(item)
                    logger.debug(f"提取到常规模块: {item['title']}")
            else:
                logger.warning(f"跳过不包含标题的项: {str(item)[:50]}...")
        
        logger.info(f"共提取出{len(regular_modules)}个常规模块和{len(simple_modules)}个简单模块")
        return regular_modules, simple_modules
    
    @staticmethod
    def categorize_modules(modules: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        根据模块类型对模块进行分类
        
        返回:
            按类型分类的模块字典
        """
        categorized = {}
        
        for module in modules:
            # 确定模块类型
            module_type = "unknown"
            
            # 根据模块特征确定类型
            if 'data' in module and isinstance(module['data'], list) and len(module['data']) > 0:
                module_type = "data_table"
            elif 'kpiValue' in module:
                module_type = "kpi_indicator"
            elif 'lineNodes' in module or 'eventNodes' in module:
                module_type = "process_flow"
            elif 'variantList' in module:
                module_type = "variant_analysis"
            
            # 添加到相应的类别
            if module_type not in categorized:
                categorized[module_type] = []
            
            categorized[module_type].append(module)
            
        return categorized