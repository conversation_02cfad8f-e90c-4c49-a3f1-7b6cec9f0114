import argparse
from typing import Dict, Any

def parse_args() -> Dict[str, Any]:
    """解析命令行参数并返回参数字典"""
    parser = argparse.ArgumentParser(description="流程挖掘分析报告生成器")
    
    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 生成报告命令
    generate_parser = subparsers.add_parser("generate", help="生成分析报告")
    generate_parser.add_argument("json_path", help="JSON数据文件的路径")
    generate_parser.add_argument("--output", "-o", help="输出文档的路径")
    generate_parser.add_argument("--log-level", help="日志级别", choices=["DEBUG", "INFO", "WARNING", "ERROR"], default="INFO")
    
    # 批量处理命令
    batch_parser = subparsers.add_parser("batch", help="批量处理多个JSON文件")
    batch_parser.add_argument("input_dir", help="输入目录，包含JSON文件")
    batch_parser.add_argument("--output-dir", "-o", help="输出目录")
    batch_parser.add_argument("--pattern", "-p", help="文件匹配模式", default="*.json")
    
    # 转换命令
    convert_parser = subparsers.add_parser("convert", help="转换JSON数据格式")
    convert_parser.add_argument("input_file", help="输入JSON文件")
    convert_parser.add_argument("output_file", help="输出JSON文件")
    convert_parser.add_argument("--format", "-f", help="输出格式", choices=["compact", "pretty"], default="pretty")
    
    args = parser.parse_args()
    
    # 如果没有提供命令，显示帮助并退出
    if not args.command:
        parser.print_help()
        exit(1)
    
    return vars(args)