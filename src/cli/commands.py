import os
import glob
import json
import logging
from typing import Dict, Any, List

from src.main import process_report
from src.utils.file_utils import ensure_dir_exists, safe_filename
from src.utils.time_utils import get_timestamp

logger = logging.getLogger(__name__)

def cmd_generate(args: Dict[str, Any]) -> int:
    """
    执行生成报告命令
    
    参数:
        args: 命令行参数
        
    返回:
        退出码
    """
    try:
        output_file = process_report(args["json_path"], args.get("output"))
        print(f"报告已成功生成: {output_file}")
        return 0
    except Exception as e:
        print(f"错误: {str(e)}")
        return 1

def cmd_batch(args: Dict[str, Any]) -> int:
    """
    执行批量处理命令
    
    参数:
        args: 命令行参数
        
    返回:
        退出码
    """
    input_dir = args["input_dir"]
    output_dir = args.get("output_dir", "output/batch")
    pattern = args.get("pattern", "*.json")
    
    # 确保输出目录存在
    ensure_dir_exists(output_dir)
    
    # 查找所有匹配的文件
    file_pattern = os.path.join(input_dir, pattern)
    files = glob.glob(file_pattern)
    
    if not files:
        print(f"未找到匹配的文件: {file_pattern}")
        return 1
    
    print(f"找到{len(files)}个匹配的文件")
    
    success_count = 0
    error_count = 0
    results = []
    
    for file_path in files:
        file_name = os.path.basename(file_path)
        base_name = os.path.splitext(file_name)[0]
        safe_name = safe_filename(base_name)
        
        output_path = os.path.join(output_dir, f"{safe_name}_{get_timestamp()}.docx")
        
        print(f"处理: {file_path} -> {output_path}")
        
        try:
            result = process_report(file_path, output_path)
            print(f"成功: {result}")
            success_count += 1
            results.append({"input": file_path, "output": result, "status": "success"})
        except Exception as e:
            print(f"失败: {str(e)}")
            error_count += 1
            results.append({"input": file_path, "error": str(e), "status": "error"})
    
    # 写入批处理结果
    result_path = os.path.join(output_dir, f"batch_results_{get_timestamp()}.json")
    with open(result_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"批处理完成: 成功 {success_count}, 失败 {error_count}")
    print(f"结果已保存到: {result_path}")
    
    return 0 if error_count == 0 else 1

def cmd_convert(args: Dict[str, Any]) -> int:
    """
    执行转换命令
    
    参数:
        args: 命令行参数
        
    返回:
        退出码
    """
    input_file = args["input_file"]
    output_file = args["output_file"]
    output_format = args.get("format", "pretty")
    
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            if output_format == "pretty":
                json.dump(data, f, ensure_ascii=False, indent=2)
            else:
                json.dump(data, f, ensure_ascii=False, separators=(',', ':'))
        
        print(f"转换成功: {input_file} -> {output_file}")
        return 0
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return 1

def run_command(args: Dict[str, Any]) -> int:
    """
    根据命令分发到相应的处理函数
    
    参数:
        args: 命令行参数
        
    返回:
        退出码
    """
    command = args.get("command")
    
    if command == "generate":
        return cmd_generate(args)
    elif command == "batch":
        return cmd_batch(args)
    elif command == "convert":
        return cmd_convert(args)
    else:
        print(f"未知命令: {command}")
        return 1