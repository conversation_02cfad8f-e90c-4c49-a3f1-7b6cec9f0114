from datetime import datetime
import time

def get_timestamp() -> str:
    """生成当前日期和时间的字符串表示"""
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def get_formatted_date() -> str:
    """获取格式化的日期字符串"""
    return datetime.now().strftime("%Y年%m月%d日")

def time_function(func):
    """函数计时装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} 耗时: {end_time - start_time:.2f}秒")
        return result
    return wrapper