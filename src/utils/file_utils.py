import os
import shutil
import tempfile
from typing import Optional, List
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def ensure_dir_exists(directory: str) -> bool:
    """
    确保目录存在，如果不存在则创建
    
    参数:
        directory: 目录路径
        
    返回:
        是否成功
    """
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"创建目录失败: {directory}, 错误: {str(e)}")
        return False

def create_temp_file(content: str, suffix: Optional[str] = None) -> Optional[str]:
    """
    创建临时文件并写入内容
    
    参数:
        content: 文件内容
        suffix: 文件后缀
        
    返回:
        临时文件路径或None（如果失败）
    """
    try:
        fd, path = tempfile.mkstemp(suffix=suffix)
        with os.fdopen(fd, 'w', encoding='utf-8') as f:
            f.write(content)
        return path
    except Exception as e:
        logger.error(f"创建临时文件失败: {str(e)}")
        return None

def safe_filename(name: str) -> str:
    """
    将字符串转换为安全的文件名
    
    参数:
        name: 原始字符串
        
    返回:
        安全的文件名
    """
    # 替换不安全的字符
    safe_chars = "-_.() abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    return ''.join(c if c in safe_chars else '_' for c in name)

def get_files_with_extension(directory: str, extension: str) -> List[str]:
    """
    获取目录中具有特定扩展名的所有文件
    
    参数:
        directory: 目录路径
        extension: 文件扩展名（例如 '.json'）
        
    返回:
        文件路径列表
    """
    files = []
    try:
        for entry in os.scandir(directory):
            if entry.is_file() and entry.name.endswith(extension):
                files.append(entry.path)
        return files
    except Exception as e:
        logger.error(f"获取文件失败: {str(e)}")
        return []