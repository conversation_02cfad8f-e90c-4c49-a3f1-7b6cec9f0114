import logging
import os
import sys
from pathlib import Path
from typing import Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import LOG_LEVEL, LOG_FILE

def setup_logging(log_level: Optional[str] = None, log_file: Optional[str] = None):
    """
    设置日志记录
    
    参数:
        log_level: 日志级别
        log_file: 日志文件路径
    """
    # 使用配置中的默认值（如果未提供）
    level = log_level or LOG_LEVEL
    file_path = log_file or LOG_FILE
    
    # 确保日志目录存在
    log_dir = os.path.dirname(file_path)
    os.makedirs(log_dir, exist_ok=True)
    
    # 设置日志级别
    numeric_level = getattr(logging, level.upper(), None)
    if not isinstance(numeric_level, int):
        numeric_level = logging.INFO
    
    # 设置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 配置根日志记录器
    logging.basicConfig(
        level=numeric_level,
        format=log_format,
        datefmt=date_format,
        handlers=[
            logging.FileHandler(file_path),
            logging.StreamHandler()
        ]
    )
    
    # 设置第三方日志记录器的级别
    logging.getLogger('openai').setLevel(logging.WARNING)
    logging.getLogger('docx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    logging.info(f"日志记录已设置，级别: {level}，文件: {file_path}")