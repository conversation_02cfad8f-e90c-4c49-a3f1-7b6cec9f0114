import os
import logging
import time
import re
from docx import Document
from docx.shared import Pt, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_BREAK

logger = logging.getLogger(__name__)

class TocGenerator:
    """目录生成器，只显示主章节标题（大标题），防止重复出现目录和章节"""
    
    @staticmethod
    def update_toc(doc_path):
        """
        更新Word文档中的目录，只创建主章节标题，避免重复
        
        参数:
            doc_path: Word文档路径
            
        返回:
            是否成功更新
        """
        try:
            # 确保文件存在
            if not os.path.exists(doc_path):
                logger.error(f"文档不存在: {doc_path}")
                return False
            
            # 使用直接文本创建目录并防止重复
            return TocGenerator._fix_duplicated_toc(doc_path)
                
        except Exception as e:
            logger.error(f"更新目录时出错: {str(e)}")
            return False
    
    @staticmethod
    def _fix_duplicated_toc(doc_path):
        """
        修复文档中重复的目录问题并创建干净的目录
        """
        try:
            # 尝试打开文档，可能需要多次尝试
            max_attempts = 5
            doc = None
            
            for attempt in range(max_attempts):
                try:
                    doc = Document(doc_path)
                    break
                except Exception as e:
                    if attempt == max_attempts - 1:
                        logger.error(f"无法打开文档: {str(e)}")
                        return False
                    logger.warning(f"尝试打开文档失败，重试中... ({attempt+1}/{max_attempts})")
                    time.sleep(2)
            
            if doc is None:
                return False
            
            # 1. 找到所有"目录"标题的索引
            toc_indices = [i for i, para in enumerate(doc.paragraphs) if para.text.strip() == "目录"]
            if not toc_indices:
                logger.warning("未找到目录标题")
                return False
            first_toc = toc_indices[0]
            
            # 2. 找到所有主章节标题（一、二、三等开头的标题）
            # 只匹配大标题格式：一、二、三等开头的标题
            chapter_pattern = re.compile(r"^([一二三四五六七八九十]+)、")
            
            # 3. 删除所有多余"目录"标题及其后到下一个章节标题之间的内容（只保留第一个目录）
            chapter_indices = []
            for i, para in enumerate(doc.paragraphs):
                if chapter_pattern.match(para.text.strip()):
                    chapter_indices.append(i)
            
            for idx in reversed(toc_indices[1:]):
                next_chapter = next((i for i in chapter_indices if i > idx), len(doc.paragraphs))
                for i in range(next_chapter - 1, idx - 1, -1):
                    p = doc.paragraphs[i]._p
                    parent = p.getparent()
                    if parent is not None:
                        parent.remove(p)
            
            # 4. 删除第一个"目录"标题后到下一个章节标题之间的内容（旧目录内容）
            next_chapter = next((i for i in chapter_indices if i > first_toc), len(doc.paragraphs))
            for i in range(next_chapter - 1, first_toc, -1):
                p = doc.paragraphs[i]._p
                parent = p.getparent()
                if parent is not None:
                    parent.remove(p)
            
            # 5. 提取所有主章节标题（只包含"一、"、"二、"等开头的标题）
            main_headings = []
            
            for para in doc.paragraphs:
                text = para.text.strip()
                if chapter_pattern.match(text):
                    main_headings.append(text)
            
            # 确保所有标题被正确识别
            if not main_headings:
                logger.warning("未找到任何主章节标题")
                return False
                
            logger.info(f"找到以下主章节标题: {main_headings}")
            
            # 6. 在第一个目录标题后插入目录内容（main_headings）
            insert_index = first_toc + 1
            # 添加空行
            empty_para = doc.add_paragraph()
            p_element = empty_para._p
            doc._body._body.remove(p_element)
            doc._body._body.insert(insert_index, p_element)
            insert_index += 1
            
            # 创建新的目录（只包含主章节标题）
            for heading in main_headings:
                chapter_para = doc.add_paragraph()
                chapter_para.paragraph_format.left_indent = Inches(0)
                chapter_para.paragraph_format.space_after = Pt(12)
                chapter_run = chapter_para.add_run(heading)
                chapter_run.font.size = Pt(12)
                chapter_run.font.bold = True
                p_element = chapter_para._p
                doc._body._body.remove(p_element)
                doc._body._body.insert(insert_index, p_element)
                insert_index += 1
                
            # 添加空行
            empty_para = doc.add_paragraph()
            p_element = empty_para._p
            doc._body._body.remove(p_element)
            doc._body._body.insert(insert_index, p_element)
            insert_index += 1
            
            # 添加分页符
            try:
                page_break_para = doc.add_paragraph()
                run = page_break_para.add_run()
                run.add_break(WD_BREAK.PAGE)
                p_element = page_break_para._p
                doc._body._body.remove(p_element)
                doc._body._body.insert(insert_index, p_element)
            except Exception as e:
                logger.warning(f"添加分页符失败: {str(e)}")
            
            # 保存文档
            doc.save(doc_path)
            logger.info(f"成功创建目录并修复重复问题: {doc_path}")
            return True
            
        except Exception as e:
            logger.error(f"修复目录时出错: {str(e)}")
            return False