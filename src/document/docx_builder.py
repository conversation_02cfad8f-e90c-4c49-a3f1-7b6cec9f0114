from docx import Document
from docx.shared import Pt, Inches, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
import os
import sys
import re
import logging
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime
import base64
import io

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import OUTPUT_DIR, DEFAULT_OUTPUT_FILENAME
from config.doc_styles import TITLE_STYLE, HEADING1_STYLE, PARAGRAPH_STYLE, PAGE_SETTINGS

logger = logging.getLogger(__name__)

class DocxBuilder:
    """Word文档构建器，创建和管理流程挖掘分析报告文档"""
    
    def __init__(self, output_path: Optional[str] = None):
        """初始化文档构建器"""
        self.document = Document()
        self.sections = []  # 保存所有标题，用于生成目录
        self.current_section_number = 0  # 当前章节编号
        self.chinese_numbers = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
                               "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十"]
        
        # 设置文档属性
        self.document.core_properties.title = "流程挖掘分析报告"
        self.document.core_properties.author = "流程挖掘分析报告生成器"
        self.document.core_properties.created = datetime.now()
        
        # 设置页面格式
        section = self.document.sections[0]
        section.page_height = Inches(11)
        section.page_width = Inches(8.5)
        section.left_margin = PAGE_SETTINGS['left_margin']
        section.right_margin = PAGE_SETTINGS['right_margin']
        section.top_margin = PAGE_SETTINGS['top_margin']
        section.bottom_margin = PAGE_SETTINGS['bottom_margin']
        
        # 创建标题样式
        self._create_heading_styles()
        
        # 设置输出路径
        if output_path:
            self.output_path = output_path
        else:
            # 如果输出目录不存在，则创建
            os.makedirs(OUTPUT_DIR, exist_ok=True)
            self.output_path = os.path.join(OUTPUT_DIR, DEFAULT_OUTPUT_FILENAME)
            
        logger.info("文档构建器初始化完成")
    
    def _create_heading_styles(self):
        """创建并设置标题样式"""
        # 创建自定义标题样式
        styles = self.document.styles
        
        # 修改默认的标题样式以匹配需求
        heading1_style = styles['Heading 1']
        heading1_style.font.name = HEADING1_STYLE['font_name']
        heading1_style.font.size = HEADING1_STYLE['font_size']
        heading1_style.font.bold = HEADING1_STYLE['bold']
        heading1_style.font.color.rgb = HEADING1_STYLE['color']
        
        # 创建不带编号的标题样式专门用于目录标题
        if 'TOC Heading' not in styles:
            toc_style = styles.add_style('TOC Heading', WD_STYLE_TYPE.PARAGRAPH)
            toc_style.base_style = styles['Normal']
            toc_style.font.name = HEADING1_STYLE['font_name']
            toc_style.font.size = HEADING1_STYLE['font_size']
            toc_style.font.bold = HEADING1_STYLE['bold']
            toc_style.font.color.rgb = HEADING1_STYLE['color']
        
        # 创建二级标题样式
        if 'Heading 2 Custom' not in styles:
            heading2_style = styles.add_style('Heading 2 Custom', WD_STYLE_TYPE.PARAGRAPH)
            heading2_style.base_style = styles['Normal']
            heading2_style.font.name = PARAGRAPH_STYLE['font_name']
            heading2_style.font.size = Pt(14)
            heading2_style.font.bold = True
    
    def _clean_text(self, text: str) -> str:
        """清理文本中的格式符号"""
        # 移除井号标记（####）
        text = re.sub(r'#+\s+', '', text)
        
        # 移除破折号标记（---）
        text = re.sub(r'\n\s*---\s*\n', '\n\n', text)
        
        # 处理无序列表项，保留文本但移除前面的破折号或星号
        text = re.sub(r'\n\s*[-*]\s+', '\n', text)
        
        # 移除星号强调（**文本**）保留文本内容
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
        
        # 移除单星号强调（*文本*）保留文本内容
        text = re.sub(r'(?<!\*)\*(?!\*)(.*?)(?<!\*)\*(?!\*)', r'\1', text)
        
        # 移除反引号代码块（`文本`）保留文本内容
        text = re.sub(r'`(.*?)`', r'\1', text)
        
        # 移除多余的空行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        return text.strip()
    
    def add_title_page(self, title: str = "流程挖掘分析报告", subtitle: str = None):
        """添加标题页"""
        # 添加标题
        title_para = self.document.add_heading(level=0)
        title_run = title_para.add_run(title)
        title_run.font.size = TITLE_STYLE['font_size']
        title_run.font.name = TITLE_STYLE['font_name']
        title_run.font.bold = TITLE_STYLE['bold']
        title_run.font.color.rgb = TITLE_STYLE['color']
        title_para.alignment = TITLE_STYLE['alignment']
        title_para.space_after = TITLE_STYLE['space_after']
        
        # 添加副标题
        if subtitle:
            subtitle_para = self.document.add_paragraph()
            subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            subtitle_run = subtitle_para.add_run(subtitle)
            subtitle_run.font.size = Pt(16)
            subtitle_run.font.italic = True
        
        # 添加生成日期
        date_para = self.document.add_paragraph()
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        date_run = date_para.add_run(f"生成日期: {datetime.now().strftime('%Y年%m月%d日')}")
        date_run.font.size = Pt(12)
        
        # 添加分页符
        self.document.add_page_break()
        
        logger.info("添加标题页")
    
    def add_table_of_contents(self):
        """添加目录页（空目录，稍后会填充内容）"""
        # 添加目录标题，使用特殊样式
        para = self.document.add_paragraph("目录", style='TOC Heading')
        para.alignment = WD_ALIGN_PARAGRAPH.LEFT
        
        # 添加一个空段落作为目录内容的占位符
        empty_para = self.document.add_paragraph()
        empty_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
        
        # 添加分页符
        self.document.add_page_break()
        
        logger.info("添加目录页")
    
    def add_numbered_content(self, text: str, index: int = None):
        """添加内容点"""
        para = self.document.add_paragraph()
        
        # 如果文本已经以点符号开头，直接使用文本
        if text.strip().startswith('·'):
            run = para.add_run(text)
        else:
            # 否则添加点符号
            run = para.add_run(f"· {text}")
        
        run.font.name = PARAGRAPH_STYLE['font_name']
        run.font.size = PARAGRAPH_STYLE['font_size']
        return para
        
    def add_image_from_base64(self, base64_string: str, width: Optional[float] = 6.0):
        """
        从base64字符串添加图片到文档
        
        参数:
            base64_string: base64编码的图片数据
            width: 图片宽度（英寸）
        """
        try:
            # 移除可能的前缀
            if "," in base64_string:
                base64_string = base64_string.split(",")[1]
            elif "data:image" in base64_string:
                # 获取"data:image/xxx;base64,"后面的部分
                base64_string = base64_string.split("base64,")[1] if "base64," in base64_string else base64_string
            
            # 解码base64数据
            image_data = base64.b64decode(base64_string)
            
            # 创建一个内存流
            image_stream = io.BytesIO(image_data)
            
            # 添加图片到文档
            self.document.add_picture(image_stream, width=Inches(width))
            
            logger.info("成功添加base64图片到文档")
            
        except Exception as e:
            logger.error(f"添加base64图片失败: {str(e)}")
            para = self.document.add_paragraph()
            run = para.add_run("【图片加载失败】")
            run.font.color.rgb = RGBColor(255, 0, 0)
    
    def _parse_ai_content(self, content: str, is_summary: bool = False):
        """
        解析AI生成的内容，处理三级结构：一级小标题、二级小标题、内容点
        
        参数:
            content: AI生成的原始内容
            is_summary: 是否是报告摘要内容
            
        返回:
            解析后的结构化内容
        """
        lines = content.split('\n')
        structured_content = []
        current_section = None
        current_subsection = None
        current_points = []
        intro_points = []
        found_first_section = False
        
        # 定义应该被识别为二级标题的关键词
        subsection_keywords = [
            '总结', '小结', '结论', '分析', '建议', '问题', '特征', '现状', 
            '背景', '概述', '说明', '要点', '核心', '关键', '重点', '发现',
            '洞察', '启示', '价值', '意义', '影响', '趋势', '展望', '机会',
            '挑战', '风险', '对策', '措施', '方案', '策略', '效果', '评估'
        ]
        
        # 需要过滤掉的标记词列表
        filter_terms = ['内容:', '内容：']
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 过滤掉特定的标记词
            if line in filter_terms:
                continue
            
            # 检查是否是一级小标题（形如"1、数据解读"）
            if re.match(r'^\d+、', line):
                # 保存之前的内容
                if current_section:
                    if current_subsection and current_points:
                        # 如果有二级小标题，保存当前的内容点
                        current_section['subsections'].append({
                            'title': current_subsection,
                            'points': current_points
                        })
                    elif current_points:
                        # 如果没有二级小标题但有内容点，这些点直接属于一级标题
                        # 不要添加默认的"内容"标题
                        current_section['direct_points'] = current_points
                    
                    # 保存当前section
                    structured_content.append(current_section)
                    
                # 开始新的一级section
                section_title = line
                # 对于摘要内容，保留完整的标题（包括编号）
                # 对于非摘要内容，去掉编号
                if not is_summary:
                    section_title = re.sub(r'^\d+、\s*', '', line)
                
                # 创建新的section对象
                current_section = {
                    'title': section_title,
                    'subsections': [],
                    'direct_points': []  # 直接属于一级标题的点，不需要二级标题
                }
                current_subsection = None
                current_points = []
                found_first_section = True
                
            # 检查是否是二级小标题（不带编号，以冒号结尾）
            elif (line.endswith('：') or line.endswith(':')) and (line.rstrip('：:') in subsection_keywords or '：' not in line[:-1]):
                # 保存之前的二级小标题内容
                if current_section and current_subsection and current_points:
                    current_section['subsections'].append({
                        'title': current_subsection,
                        'points': current_points
                    })
                
                # 开始新的二级小标题
                current_subsection = line.rstrip('：:')
                current_points = []
            
            # 检查是否是点格式或编号格式的内容点
            elif line.startswith('·') or re.match(r'^\(\d+\)', line) or re.match(r'^（\d+）', line):
                # 统一处理为点格式内容
                if line.startswith('·'):
                    content_text = line
                else:
                    # 将编号格式转换为点格式
                    content_text = re.sub(r'^\(\d+\)\s*|^（\d+）\s*', '· ', line)
                
                if not found_first_section:
                    intro_points.append(content_text)
                else:
                    current_points.append(content_text)
            
            # 其他内容
            else:
                clean_content = re.sub(r'^\d+、\s*', '', line)
                if not found_first_section:
                    intro_points.append(clean_content)
                else:
                    current_points.append(clean_content)
        
        # 保存最后的内容
        if current_section:
            if current_subsection and current_points:
                current_section['subsections'].append({
                    'title': current_subsection,
                    'points': current_points
                })
            elif current_points:
                # 如果没有二级小标题但有内容点，添加到direct_points
                current_section['direct_points'] = current_points
            
            if current_section['subsections'] or current_section['direct_points']:
                structured_content.append(current_section)
        elif intro_points:
            structured_content.append({
                'title': "内容概述",
                'subsections': [],
                'direct_points': intro_points
            })
        
        return structured_content
        
    def add_executive_summary(self, summary_text: str):
        """添加报告摘要（作为最后一个章节）"""
        # 递增章节编号
        self.current_section_number += 1
        chinese_number = self.chinese_numbers[self.current_section_number - 1]
        
        # 清理文本内容
        cleaned_summary = self._clean_text(summary_text)
        
        # 添加带编号的标题（使用中文数字）
        heading_text = f"{chinese_number}、报告摘要"
        heading = self.document.add_heading(heading_text, level=1)
        
        # 应用标题样式
        if heading.runs:
            heading.runs[0].font.name = HEADING1_STYLE['font_name']
            heading.runs[0].font.size = HEADING1_STYLE['font_size']
            heading.runs[0].font.bold = HEADING1_STYLE['bold']
            heading.runs[0].font.color.rgb = HEADING1_STYLE['color']
        
        # 记录章节标题用于目录
        self.sections.append((heading_text, 1))
        
        # 解析AI生成的内容（标记为摘要内容）
        structured_content = self._parse_ai_content(cleaned_summary, is_summary=True)
        
        # 添加解析后的内容
        section_counter = 0
        for section_data in structured_content:
            if isinstance(section_data, dict):
                section_counter += 1
                section_title = section_data['title']
                
                # 对于摘要内容，去掉原有编号，使用新的编号
                # 如果标题已经有编号（如"1、用户旅程..."），先去掉它
                section_title = re.sub(r'^\d+、\s*', '', section_title)
                
                # 添加一级小标题（统一编号）
                subheading_para = self.document.add_paragraph(style='Heading 2 Custom')
                subheading_run = subheading_para.add_run(f"{section_counter}、{section_title}")
                subheading_run.font.bold = True
                
                # 记录二级标题用于目录
                self.sections.append((f"{section_counter}、{section_title}", 2))
                
                # 先处理直接属于一级标题的内容点
                for i, point in enumerate(section_data.get('direct_points', []), 1):
                    self.add_numbered_content(point, i)
                
                # 处理二级小标题和内容点
                for subsection in section_data.get('subsections', []):
                    # 只有当小标题不是"内容"时才添加
                    if subsection['title'] != "内容":
                        # 添加二级小标题
                        subsection_para = self.document.add_paragraph()
                        subsection_run = subsection_para.add_run(f"{subsection['title']}：")
                        subsection_run.font.bold = True
                        subsection_run.font.size = Pt(12)
                    
                    # 添加内容点
                    for i, point in enumerate(subsection.get('points', []), 1):
                        self.add_numbered_content(point, i)
        
        logger.info(f"添加报告摘要: {heading_text}")
    
    def add_section(self, title: str, content: str, level: int = 1):
        """
        添加报告部分
        
        参数:
            title: 部分标题
            content: 部分内容
            level: 标题级别 (1-3)
        """
        # 清理标题中的特殊符号
        title = re.sub(r'[*#@]+\s*(.*?)\s*[*#@]+', r'\1', title)
        title = re.sub(r'[*#@]', '', title)
        # 清理文本内容
        cleaned_content = self._clean_text(content)
        
        # 处理一级标题
        if level == 1:
            # 递增章节编号
            self.current_section_number += 1
            chinese_number = self.chinese_numbers[self.current_section_number - 1]
            
            # 添加带编号的标题（使用中文数字）
            heading_text = f"{chinese_number}、{title}"
            heading = self.document.add_heading(heading_text, level=level)
            
            # 应用标题样式
            if heading.runs:
                heading.runs[0].font.name = HEADING1_STYLE['font_name']
                heading.runs[0].font.size = HEADING1_STYLE['font_size']
                heading.runs[0].font.bold = HEADING1_STYLE['bold']
                heading.runs[0].font.color.rgb = HEADING1_STYLE['color']
            
            # 记录章节标题用于目录
            self.sections.append((heading_text, level))
            
            # 解析AI生成的内容（非摘要内容）
            structured_content = self._parse_ai_content(cleaned_content, is_summary=False)
            
            # 添加解析后的内容
            section_counter = 0
            for section_data in structured_content:
                if isinstance(section_data, dict):
                    # 新的三级结构处理
                    section_counter += 1
                    section_title = section_data['title']
                    
                    # 添加一级小标题（重新编号）
                    subheading_para = self.document.add_paragraph(style='Heading 2 Custom')
                    subheading_run = subheading_para.add_run(f"{section_counter}、{section_title}")
                    subheading_run.font.bold = True
                    
                    # 记录二级标题用于目录
                    self.sections.append((f"{section_counter}、{section_title}", 2))
                    
                    # 先处理直接属于一级标题的内容点
                    for i, point in enumerate(section_data.get('direct_points', []), 1):
                        self.add_numbered_content(point, i)
                    
                    # 处理二级小标题和内容点
                    for subsection in section_data.get('subsections', []):
                        # 只有当小标题不是"内容"时才添加
                        if subsection['title'] != "内容":
                            # 添加二级小标题（不编号）
                            subsection_para = self.document.add_paragraph()
                            subsection_run = subsection_para.add_run(f"{subsection['title']}：")
                            subsection_run.font.bold = True
                            subsection_run.font.size = Pt(12)
                        
                        # 添加内容点（重新编号）
                        for i, point in enumerate(subsection.get('points', []), 1):
                            self.add_numbered_content(point, i)
                else:
                    # 兼容旧的二元组格式
                    section_title, points = section_data
                    section_counter += 1
                    
                    # 添加二级标题（重新编号）
                    subheading_para = self.document.add_paragraph(style='Heading 2 Custom')
                    subheading_run = subheading_para.add_run(f"{section_counter}、{section_title}")
                    subheading_run.font.bold = True
                    
                    # 记录二级标题用于目录
                    self.sections.append((f"{section_counter}、{section_title}", 2))
                    
                    # 添加内容点（重新编号）
                    for i, point in enumerate(points, 1):
                        self.add_numbered_content(point, i)
        
        # 处理二级标题
        elif level == 2:
            # 添加二级标题
            para = self.document.add_paragraph(style='Heading 2 Custom')
            run = para.add_run(f"{title}")
            run.font.bold = True
            
            # 记录标题用于目录
            self.sections.append((title, level))
            
            # 解析AI生成的内容（非摘要内容）
            structured_content = self._parse_ai_content(cleaned_content, is_summary=False)
            
            # 添加解析后的内容
            for section_data in structured_content:
                if isinstance(section_data, dict):
                    # 先处理直接属于一级标题的内容点
                    for i, point in enumerate(section_data.get('direct_points', []), 1):
                        self.add_numbered_content(point, i)
                    
                    # 处理二级小标题和内容点
                    for subsection in section_data.get('subsections', []):
                        # 只有当小标题不是"内容"时才添加
                        if subsection['title'] != "内容":
                            # 添加二级小标题
                            subsection_para = self.document.add_paragraph()
                            subsection_run = subsection_para.add_run(f"{subsection['title']}：")
                            subsection_run.font.bold = True
                        
                        # 添加内容点
                        for i, point in enumerate(subsection.get('points', []), 1):
                            self.add_numbered_content(point, i)
                else:
                    # 兼容旧格式
                    section_title, points = section_data
                    for i, point in enumerate(points, 1):
                        self.add_numbered_content(point, i)
        
        # 处理三级标题
        else:
            # 添加三级标题
            para = self.document.add_paragraph()
            run = para.add_run(title)
            run.font.bold = True
            
            # 记录标题用于目录
            self.sections.append((title, level))
            
            # 解析AI生成的内容（非摘要内容）
            structured_content = self._parse_ai_content(cleaned_content, is_summary=False)
            
            # 添加解析后的内容
            for section_data in structured_content:
                if isinstance(section_data, dict):
                    # 先处理直接属于一级标题的内容点
                    for i, point in enumerate(section_data.get('direct_points', []), 1):
                        self.add_numbered_content(point, i)
                    
                    # 处理二级小标题和内容点
                    for subsection in section_data.get('subsections', []):
                        # 只有当小标题不是"内容"时才添加
                        if subsection['title'] != "内容":
                            # 添加二级小标题
                            subsection_para = self.document.add_paragraph()
                            subsection_run = subsection_para.add_run(f"{subsection['title']}：")
                            subsection_run.font.bold = True
                        
                        # 添加内容点
                        for i, point in enumerate(subsection.get('points', []), 1):
                            self.add_numbered_content(point, i)
                else:
                    section_title, points = section_data
                    for i, point in enumerate(points, 1):
                        self.add_numbered_content(point, i)
        
        logger.info(f"添加部分: {title}")
    
    def save(self) -> str:
        """保存文档并返回文件路径"""
        try:
            logger.info(f"正在保存文档到: {self.output_path}")
            self.document.save(self.output_path)
            logger.info("文档保存成功")
            return self.output_path
        except Exception as e:
            logger.error(f"保存文档失败: {str(e)}")
            raise Exception(f"保存文档失败: {str(e)}")