import os
import sys
import logging
import argparse
from typing import Optional, Any, List  # 添加 Optional 导入
import time
# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.logging_utils import setup_logging
from src.data.json_loader import JsonLoader
from src.data.json_validator import JsonValidator
from src.data.module_extractor import ModuleExtractor
from src.ai.client import AIClient
from src.ai.title_polisher import TitlePolisher
from src.ai.content_generator import ContentGenerator
from src.document.docx_builder import DocxBuilder
from src.document.section_factory import SectionFactory
from src.document.toc_generator import TocGenerator
from src.utils.file_utils import ensure_dir_exists
from src.utils.time_utils import get_timestamp
from config.settings import OUTPUT_DIR
from config.ai_config import DEFAULT_DOMAIN

def process_report(json_path: str, output_path: Optional[str] = None, domain: Optional[str] = DEFAULT_DOMAIN) -> str:
    """
    处理JSON数据并生成流程挖掘分析报告
    
    参数:
        json_path: JSON数据文件的路径
        output_path: 输出文档的可选路径
        domain: 分析领域，用于提供专业领域的上下文和术语，默认为通用领域
        
    返回:
        生成的文档的路径
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始处理报告，JSON文件: {json_path}，领域: {domain if domain else '通用'}")
    
    try:
        # 设置输出路径
        if not output_path:
            timestamp = get_timestamp()
            output_filename = f"流程挖掘分析报告_{timestamp}.docx"
            output_path = os.path.join(OUTPUT_DIR, output_filename)
        
        # 确保输出目录存在
        ensure_dir_exists(os.path.dirname(output_path))
        
        # 加载JSON数据
        logger.info(f"加载JSON数据: {json_path}")
        data = JsonLoader.from_file(json_path)
        normalized_data = JsonLoader.normalize_data(data)
        
        # 验证数据
        valid, errors = JsonValidator.validate_all_modules(normalized_data)
        if not valid:
            logger.warning(f"数据验证发现问题: {errors}")
        
        # 提取模块
        logger.info("提取分析模块")
        regular_modules, simple_modules = ModuleExtractor.extract_modules(normalized_data)
        
        # 初始化AI组件
        ai_client = AIClient()
        title_polisher = TitlePolisher(ai_client, domain)
        content_generator = ContentGenerator(ai_client, domain)
        
        # 初始化文档组件
        docx_builder = DocxBuilder(output_path)
        section_factory = SectionFactory(docx_builder, content_generator, title_polisher)

        # 设置标准化数据到section_factory，用于摘要生成
        section_factory.set_normalized_data(normalized_data)

        # 创建报告框架
        logger.info("创建报告框架")
        docx_builder.add_title_page(subtitle="自动生成的流程挖掘分析报告")
        docx_builder.add_table_of_contents()
        
        # 首先处理常规模块
        logger.info(f"处理{len(regular_modules)}个常规模块")
        for module in regular_modules:
            section_factory.process_regular_module(module)
        
        # 然后处理简单模块（如果有）
        if simple_modules:
            logger.info(f"处理{len(simple_modules)}个简单模块")
            section_factory.process_simple_modules(simple_modules)
        
        # 最后添加报告摘要
        logger.info("生成报告摘要")
        section_factory.add_report_summary()
        
        # 完成文档
        logger.info("完成文档")
        output_file = section_factory.finalize_document()
        
        # 更新目录 - 使用新的文本式目录生成方法
        logger.info("开始生成目录")
        max_retries = 3
        success = False
        
        for attempt in range(max_retries):
            logger.info(f"尝试生成目录，第{attempt+1}次")
            try:
                # 等待文件完全保存
                time.sleep(2)
                success = TocGenerator.update_toc(output_file)
                
                if success:
                    logger.info("目录已成功生成")
                    break
                elif attempt < max_retries - 1:
                    logger.warning(f"生成目录失败，将重试...")
                    time.sleep(3)  # 增加等待时间再重试
            except Exception as e:
                logger.error(f"生成目录出错: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(3)
        
        if not success:
            logger.warning("所有目录生成尝试都失败，报告将不包含自动生成的目录")
        
        logger.info(f"报告生成成功: {output_file}")
        return output_file
        
    except Exception as e:
        logger.error(f"处理报告失败: {str(e)}", exc_info=True)
        raise Exception(f"处理报告失败: {str(e)}")

def main():
    """主函数，处理命令行参数和执行报告生成"""
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description="从JSON数据生成流程挖掘分析报告")
    parser.add_argument("json_path", help="JSON数据文件的路径")
    parser.add_argument("--output", "-o", help="输出文档的路径")
    parser.add_argument("--log-level", help="日志级别", choices=["DEBUG", "INFO", "WARNING", "ERROR"], default="INFO")
    parser.add_argument("--domain", "-d", help="分析领域，用于提供专业领域的上下文和术语", default=DEFAULT_DOMAIN)
    
    args = parser.parse_args()
    
    # 设置日志记录
    setup_logging(log_level=args.log_level)
    
    try:
        # 运行报告生成
        output_file = process_report(args.json_path, args.output, args.domain)
        print(f"报告已成功生成: {output_file}")
        return 0
    except Exception as e:
        print(f"错误: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())