version: '3.8'

services:
  flow-mining-analyzer:
    image: flow-mining-analyzer-python:latest
    container_name: flow-mining-analyzer
    ports:
      - "8000:8000"
    environment:
      - DASHSCOPE_API_KEY=${DASHSCOPE_API_KEY:-}
      - OUTPUT_DIR=/app/outputs
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
    volumes:
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
      - ./logs:/app/logs
      - ./api/uploads:/app/api/uploads
      - ./api/outputs:/app/api/outputs
      - ./api/temp:/app/api/temp
    restart: unless-stopped
    command: >
      bash -c "
        pip uninstall -y pandas numpy &&
        pip install numpy==1.24.3 pandas==2.0.3 &&
        python api/api_server.py
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - flow-mining-network

  # 可选：nginx反向代理（用于生产环境）
  nginx:
    image: nginx:alpine
    container_name: flow-mining-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - flow-mining-analyzer
    restart: unless-stopped
    networks:
      - flow-mining-network
    profiles:
      - production

networks:
  flow-mining-network:
    driver: bridge

volumes:
  uploads:
  outputs:
  logs: