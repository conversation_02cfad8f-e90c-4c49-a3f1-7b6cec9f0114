# 使用现有的flow-mining-analyzer镜像作为基础，然后添加Java支持
FROM flow-mining-analyzer:latest

# 设置环境变量避免交互式安装
ENV DEBIAN_FRONTEND=noninteractive

# 安装默认Java
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        default-jre-headless && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 复制已经构建好的JAR文件
COPY java-flow-mining-analyzer/target/flow-mining-analyzer-1.0.0.jar app.jar

# 创建必要的目录
RUN mkdir -p /app/uploads /app/outputs /app/logs && \
    chmod 755 /app/uploads /app/outputs /app/logs

# 设置环境变量
ENV JAVA_OPTS="-Xmx512m -Xms256m" \
    SERVER_PORT=8080 \
    SPRING_PROFILES_ACTIVE=docker

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
