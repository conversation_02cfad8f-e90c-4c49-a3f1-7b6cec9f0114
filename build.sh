#!/bin/bash

# Flow Mining Analyzer 部署脚本
set -e

echo "=== Flow Mining Analyzer 部署脚本 ==="

# 配置变量
IMAGE_NAME="flow-mining-analyzer-python"
IMAGE_TAG="latest"
DEPLOY_DIR="/opt/flow-mining-analyzer"

# 步骤1: 构建镜像
echo "1. 构建Docker镜像..."
docker build -f Dockerfile.python -t ${IMAGE_NAME}:${IMAGE_TAG} .

# 步骤2: 测试镜像
echo "2. 测试镜像..."
docker run -d --name test-${IMAGE_NAME} -p 8001:8000 ${IMAGE_NAME}:${IMAGE_TAG}
sleep 10
if curl -f http://localhost:8001/health; then
    echo "镜像测试通过"
else
    echo "镜像测试失败"
    exit 1
fi
docker stop test-${IMAGE_NAME}
docker rm test-${IMAGE_NAME}

# 步骤3: 导出镜像
echo "3. 导出镜像..."
docker save -o ${IMAGE_NAME}-${IMAGE_TAG}.tar ${IMAGE_NAME}:${IMAGE_TAG}
gzip ${IMAGE_NAME}-${IMAGE_TAG}.tar

# 步骤4: 创建部署包
echo "4. 创建部署包..."
mkdir -p deploy-package
cp ${IMAGE_NAME}-${IMAGE_TAG}.tar.gz deploy-package/
cp docker-compose.yml deploy-package/
cp .env.example deploy-package/
cp deploy.sh deploy-package/

echo "=== 构建完成 ==="
echo "部署包位置: deploy-package/"
echo "请将 deploy-package/ 目录传输到服务器并运行 deploy.sh"