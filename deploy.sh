#!/bin/bash

# 服务器端部署脚本
set -e

echo "=== Flow Mining Analyzer 服务器部署脚本 ==="

# 配置变量
IMAGE_NAME="flow-mining-analyzer-python"
IMAGE_TAG="latest"
DEPLOY_DIR="/opt/flow-mining-analyzer"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "安装Docker..."
    sudo apt update
    sudo apt install -y docker.io docker-compose
    sudo systemctl start docker
    sudo systemctl enable docker
    sudo usermod -aG docker $USER
fi

# 创建部署目录
echo "1. 创建部署目录..."
sudo mkdir -p ${DEPLOY_DIR}
sudo chown $USER:$USER ${DEPLOY_DIR}
cd ${DEPLOY_DIR}

# 创建必要的目录
mkdir -p uploads outputs logs api/uploads api/outputs api/temp

# 加载镜像
echo "2. 加载Docker镜像..."
if [ -f "${IMAGE_NAME}-${IMAGE_TAG}.tar.gz" ]; then
    gunzip ${IMAGE_NAME}-${IMAGE_TAG}.tar.gz
    docker load -i ${IMAGE_NAME}-${IMAGE_TAG}.tar
else
    echo "镜像文件不存在，请确保已上传镜像文件"
    exit 1
fi

# 配置环境变量
echo "3. 配置环境变量..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "请编辑 .env 文件设置您的API密钥:"
    echo "DASHSCOPE_API_KEY=your_api_key_here"
    read -p "按Enter继续..."
fi

# 启动服务
echo "4. 启动服务..."
docker-compose up -d

# 等待服务启动
echo "5. 等待服务启动..."
sleep 20

# 检查服务状态
echo "6. 检查服务状态..."
if curl -f http://localhost:8000/health; then
    echo "✓ 服务部署成功！"
    echo "API地址: http://$(hostname -I | awk '{print $1}'):8000"
else
    echo "✗ 服务启动失败，请检查日志:"
    docker-compose logs
    exit 1
fi

echo "=== 部署完成 ==="


  docker run --rm -p 8004:8000 --name flow-mining-analyzer-python flow-mining-analyzer-python -d